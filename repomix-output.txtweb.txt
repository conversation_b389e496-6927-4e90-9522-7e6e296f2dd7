This file is a merged representation of a subset of the codebase, containing specifically included files, combined into a single document by Repomix.
The content has been processed where comments have been removed, empty lines have been removed, content has been compressed (code blocks are separated by ⋮---- delimiter).

================================================================
File Summary
================================================================

Purpose:
--------
This file contains a packed representation of a subset of the repository's contents that is considered the most important context.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.

File Format:
------------
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  a. A separator line (================)
  b. The file path (File: path/to/file)
  c. Another separator line
  d. The full contents of the file
  e. A blank line

Usage Guidelines:
-----------------
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.

Notes:
------
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Only files matching these patterns are included: web/next.config.js, web/docs, web/messages, web/src, web/components.json, web/package.json, web/README.md, web/tsconfig.json, .dockerignore, web/.env.example, web/.gitignore, web/docker-compose.yml, web/Dockerfile, web/eslint.config.js
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Code comments have been removed from supported file types
- Empty lines have been removed from all files
- Content has been compressed - code blocks are separated by ⋮---- delimiter
- Files are sorted by Git change count (files with more changes are at the bottom)


================================================================
Directory Structure
================================================================
web/
  docs/
    implementation-summary.md
    interaction-flow-test.md
    streaming-improvements.md
    testing-thought-block.md
    thought-block-design-system.md
    thought-block-feature.md
  messages/
    en.json
    zh.json
  src/
    app/
      chat/
        components/
          conversation-starter.tsx
          input-box.tsx
          message-list-view.tsx
          messages-block.tsx
          research-activities-block.tsx
          research-block.tsx
          research-report-block.tsx
          site-header.tsx
          welcome.tsx
        main.tsx
        page.tsx
      editor-demo/
        components/
          learning-guide.tsx
        page.tsx
      landing/
        components/
          jumbotron.tsx
          multi-agent-visualization.tsx
          ray.tsx
          section-header.tsx
        sections/
          case-study-section.tsx
          core-features-section.tsx
          join-community-section.tsx
          multi-agent-section.tsx
        store/
          graph.ts
          index.ts
          mav-store.ts
          playbook.ts
      settings/
        dialogs/
          add-mcp-server-dialog.tsx
          settings-dialog.tsx
        tabs/
          about-en.md
          about-tab.tsx
          about-zh.md
          general-tab.tsx
          index.tsx
          mcp-tab.tsx
          types.ts
      layout.tsx
      page.tsx
    components/
      deer-flow/
        icons/
          detective.tsx
          enhance.tsx
          report-style.tsx
        fav-icon.tsx
        image.tsx
        language-switcher.tsx
        link.tsx
        loading-animation.module.css
        loading-animation.tsx
        logo.tsx
        markdown.tsx
        message-input.tsx
        rainbow-text.module.css
        rainbow-text.tsx
        report-style-dialog.tsx
        resource-mentions.tsx
        resource-suggestion.tsx
        rolling-text.tsx
        scroll-container.tsx
        theme-provider-wrapper.tsx
        theme-toggle.tsx
        toaster.tsx
        tooltip.tsx
      editor/
        generative/
          ai-completion-command.tsx
          ai-selector-commands.tsx
          ai-selector.tsx
          generative-menu-switch.tsx
        selectors/
          color-selector.tsx
          link-selector.tsx
          math-selector.tsx
          node-selector.tsx
          text-buttons.tsx
        extensions.tsx
        image-upload.ts
        index.tsx
        slash-command.tsx
      magicui/
        aurora-text.tsx
        bento-grid.tsx
        border-beam.tsx
        flickering-grid.tsx
        number-ticker.tsx
        shine-border.tsx
      ui/
        icons/
          magic.tsx
        accordion.tsx
        badge.tsx
        button.tsx
        card.tsx
        checkbox.tsx
        collapsible.tsx
        command.tsx
        dialog.tsx
        dropdown-menu.tsx
        form.tsx
        input.tsx
        label.tsx
        popover.tsx
        scroll-area.tsx
        select.tsx
        separator.tsx
        sheet.tsx
        skeleton.tsx
        slider.tsx
        switch.tsx
        tabs.tsx
        textarea.tsx
        tooltip.tsx
      theme-provider.tsx
    core/
      api/
        chat.ts
        hooks.ts
        index.ts
        mcp.ts
        podcast.ts
        prompt-enhancer.ts
        rag.ts
        resolve-service-url.ts
        types.ts
      config/
        index.ts
        types.ts
      mcp/
        index.ts
        schema.ts
        types.ts
        utils.ts
      messages/
        index.ts
        merge-message.ts
        types.ts
      rehype/
        index.ts
        rehype-split-words-into-spans.ts
      replay/
        get-replay-id.ts
        hooks.ts
        index.ts
      sse/
        fetch-stream.ts
        index.ts
        StreamEvent.ts
      store/
        index.ts
        settings-store.ts
        store.ts
      utils/
        deep-clone.ts
        index.ts
        json.ts
        markdown.ts
        time.ts
    hooks/
      use-intersection-observer.ts
      use-mobile.ts
    lib/
      utils.ts
    styles/
      globals.css
      prosemirror.css
    typings/
      md.d.ts
    env.js
    i18n.ts
  .env.example
  .gitignore
  components.json
  docker-compose.yml
  Dockerfile
  eslint.config.js
  next.config.js
  package.json
  README.md
  tsconfig.json
.dockerignore

================================================================
Files
================================================================

================
File: web/src/app/chat/main.tsx
================
import { useMemo } from "react";
import { useStore } from "~/core/store";
import { cn } from "~/lib/utils";
import { MessagesBlock } from "./components/messages-block";
import { ResearchBlock } from "./components/research-block";

================
File: web/src/app/editor-demo/components/learning-guide.tsx
================
import { useState } from "react";
import { ChevronDown, ChevronRight, Code, Lightbulb, Zap } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "~/components/ui/collapsible";
import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
interface LearningSection {
  id: string;
  title: string;
  description: string;
  level: "beginner" | "intermediate" | "advanced";
  content: {
    overview: string;
    keyPoints: string[];
    codeExample?: string;
    tips?: string[];
  };
}
⋮----
const toggleSection = (sectionId: string) =>
const getLevelColor = (level: string) =>
⋮----
open=
⋮----
<Badge className=

================
File: web/src/app/editor-demo/page.tsx
================
import { useState } from "react";
import { useTranslations } from "next-intl";
import Link from "next/link";
import { ArrowLeft, Download, Eye, Code, FileText } from "lucide-react";
import ReportEditor from "~/components/editor";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { Badge } from "~/components/ui/badge";
import { Separator } from "~/components/ui/separator";
import { ScrollArea } from "~/components/ui/scroll-area";
import { LearningGuide } from "./components/learning-guide";
⋮----
const handleMarkdownChange = (newMarkdown: string) =>
const downloadMarkdown = () =>

================
File: web/src/app/landing/components/ray.tsx
================


================
File: web/src/app/landing/components/section-header.tsx
================
export function SectionHeader({
  anchor,
  title,
  description,
}: {
  anchor?: string;
  title: React.ReactNode;
  description: React.ReactNode;
})

================
File: web/src/app/landing/store/graph.ts
================
import type { Edge, Node } from "@xyflow/react";
import {
  Brain,
  FilePen,
  MessageSquareQuote,
  Microscope,
  SquareTerminal,
  UserCheck,
  Users,
  type LucideIcon,
} from "lucide-react";
export type GraphNode = Node<{
  label: string;
  icon?: LucideIcon;
  active?: boolean;
}>;
export type Graph = {
  nodes: GraphNode[];
  edges: Edge[];
};

================
File: web/src/app/landing/store/index.ts
================


================
File: web/src/app/landing/store/mav-store.ts
================
import { create } from "zustand";
import { sleep } from "~/core/utils";
import { graph, type Graph } from "./graph";
import { playbook } from "./playbook";
⋮----
export function activateStep(stepIndex: number)
export function nextStep()
export function prevStep()
export async function play()
export function pause()
export async function togglePlay()
export function stop()

================
File: web/src/app/landing/store/playbook.ts
================


================
File: web/src/app/settings/tabs/types.ts
================
import type { LucideIcon } from "lucide-react";
import type { FunctionComponent } from "react";
import type { SettingsState } from "~/core/store";
export type Tab = FunctionComponent<{
  settings: SettingsState;
  onChange: (changes: Partial<SettingsState>) => void;
}> & {
  displayName?: string;
  icon?: LucideIcon;
  badge?: string;
};

================
File: web/src/components/deer-flow/icons/detective.tsx
================
export function Detective(

================
File: web/src/components/deer-flow/fav-icon.tsx
================
import { cn } from "~/lib/utils";
⋮----
className={cn("bg-accent h-4 w-4 rounded-full shadow-sm", className)}
      width={16}
      height={16}
      src={new URL(url).origin + "/favicon.ico"}
      alt={title}
onError=

================
File: web/src/components/deer-flow/image.tsx
================
import { memo, useCallback, useEffect, useState } from "react";
import { cn } from "~/lib/utils";
import { Tooltip } from "./tooltip";

================
File: web/src/components/deer-flow/loading-animation.module.css
================
.loadingAnimation {
.loadingAnimation > div {
.loadingAnimation.sm > div {
.loadingAnimation > div:nth-child(2) {
.loadingAnimation > div:nth-child(3) {

================
File: web/src/components/deer-flow/loading-animation.tsx
================
import { cn } from "~/lib/utils";
import styles from "./loading-animation.module.css";

================
File: web/src/components/deer-flow/logo.tsx
================
import Link from "next/link";
export function Logo()

================
File: web/src/components/deer-flow/rainbow-text.module.css
================
.animated {

================
File: web/src/components/deer-flow/rainbow-text.tsx
================
import { cn } from "~/lib/utils";
import styles from "./rainbow-text.module.css";
export function RainbowText({
  animated,
  className,
  children,
}: {
  animated?: boolean;
  className?: string;
  children?: React.ReactNode;
})

================
File: web/src/components/deer-flow/rolling-text.tsx
================
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "~/lib/utils";

================
File: web/src/components/deer-flow/theme-provider-wrapper.tsx
================
import { usePathname } from "next/navigation";
import { ThemeProvider } from "~/components/theme-provider";

================
File: web/src/components/deer-flow/theme-toggle.tsx
================
import { Monitor, Moon, Sun } from "lucide-react";
import { useTheme } from "next-themes";
import { Button } from "~/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { cn } from "~/lib/utils";
import { Tooltip } from "./tooltip";

================
File: web/src/components/deer-flow/toaster.tsx
================
import { useTheme } from "next-themes";
import { Toaster as Sonner } from "sonner";
type ToasterProps = React.ComponentProps<typeof Sonner>;
const Toaster = (

================
File: web/src/components/deer-flow/tooltip.tsx
================
import type { CSSProperties } from "react";
import {
  Tooltip as ShadcnTooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "~/components/ui/tooltip";
import { cn } from "~/lib/utils";

================
File: web/src/components/editor/generative/ai-completion-command.tsx
================
import { CommandGroup, CommandItem, CommandSeparator } from "../../ui/command";
import { useEditor } from "novel";
import { Check, TextQuote, TrashIcon } from "lucide-react";
const AICompletionCommands = ({
  completion,
  onDiscard,
}: {
  completion: string;
onDiscard: ()

================
File: web/src/components/editor/generative/ai-selector-commands.tsx
================
import {
  ArrowDownWideNarrow,
  CheckCheck,
  RefreshCcwDot,
  StepForward,
  WrapText,
} from "lucide-react";
import { getPrevText, useEditor } from "novel";
import { CommandGroup, CommandItem, CommandSeparator } from "../../ui/command";
⋮----
interface AISelectorCommandsProps {
  onSelect: (value: string, option: string) => void;
}

================
File: web/src/components/editor/generative/ai-selector.tsx
================
import { Command, CommandInput } from "../../ui/command";
import { ArrowUp } from "lucide-react";
import { useEditor } from "novel";
import { addAIHighlight } from "novel";
import { useCallback, useState } from "react";
import Markdown from "react-markdown";
import { toast } from "sonner";
import { Button } from "../../ui/button";
import Magic from "../../ui/icons/magic";
import { ScrollArea } from "../../ui/scroll-area";
import AICompletionCommands from "./ai-completion-command";
import AISelectorCommands from "./ai-selector-commands";
import { LoadingOutlined } from "@ant-design/icons";
import { resolveServiceURL } from "~/core/api/resolve-service-url";
import { fetchStream } from "~/core/sse";
interface AISelectorProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}
function useProseCompletion()
⋮----
// Process the streaming response
⋮----
complete(value,

================
File: web/src/components/editor/generative/generative-menu-switch.tsx
================
import { EditorBubble, removeAIHighlight, useEditor } from "novel";
import { Fragment, type ReactNode, useEffect } from "react";
import { Button } from "../../ui/button";
import Magic from "../../ui/icons/magic";
import { AISelector } from "./ai-selector";
import { useReplay } from "~/core/replay";
import { TooltipContent, TooltipTrigger } from "~/components/ui/tooltip";
import { Tooltip } from "~/components/ui/tooltip";
interface GenerativeMenuSwitchProps {
  children: ReactNode;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

================
File: web/src/components/editor/selectors/color-selector.tsx
================
import { Check, ChevronDown } from "lucide-react";
import { EditorBubbleItem, useEditor } from "novel";
import { Button } from "../../ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "../../ui/popover";
export interface BubbleColorMenuItem {
  name: string;
  color: string;
}
⋮----
interface ColorSelectorProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

================
File: web/src/components/editor/selectors/link-selector.tsx
================
import { Button } from "../../ui/button";
import { PopoverContent } from "../../ui/popover";
import { cn } from "../../../lib/utils";
import { Popover, PopoverTrigger } from "@radix-ui/react-popover";
import { Check, Trash } from "lucide-react";
import { useEditor } from "novel";
import { useEffect, useRef } from "react";
export function isValidUrl(url: string)
export function getUrlFromString(str: string)
interface LinkSelectorProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

================
File: web/src/components/editor/selectors/math-selector.tsx
================
import { Button } from "../../ui/button";
import { cn } from "../../../lib/utils";
import { SigmaIcon } from "lucide-react";
import { useEditor } from "novel";

================
File: web/src/components/editor/selectors/node-selector.tsx
================
import {
  Check,
  CheckSquare,
  ChevronDown,
  Code,
  Heading1,
  Heading2,
  Heading3,
  ListOrdered,
  type LucideIcon,
  TextIcon,
  TextQuote,
} from "lucide-react";
import { EditorBubbleItem, useEditor } from "novel";
import { Button } from "../../ui/button";
import { PopoverContent, PopoverTrigger } from "../../ui/popover";
import { Popover } from "@radix-ui/react-popover";
export type SelectorItem = {
  name: string;
  icon: LucideIcon;
  command: (
    editor: NonNullable<ReturnType<typeof useEditor>["editor"]>,
  ) => void;
  isActive: (
    editor: NonNullable<ReturnType<typeof useEditor>["editor"]>,
  ) => boolean;
};
⋮----
interface NodeSelectorProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}
⋮----
item.command(editor);
onOpenChange(false);

================
File: web/src/components/editor/selectors/text-buttons.tsx
================
import { Button } from "../../ui/button";
import { cn } from "../../../lib/utils";
import {
  BoldIcon,
  CodeIcon,
  ItalicIcon,
  StrikethroughIcon,
  UnderlineIcon,
} from "lucide-react";
import { EditorBubbleItem, useEditor } from "novel";
import type { SelectorItem } from "./node-selector";
⋮----
item.command(editor);

================
File: web/src/components/editor/extensions.tsx
================
import {
  AIHighlight,
  CharacterCount,
  CodeBlockLowlight,
  Color,
  CustomKeymap,
  GlobalDragHandle,
  HighlightExtension,
  HorizontalRule,
  Mathematics,
  Placeholder,
  StarterKit,
  TaskItem,
  TaskList,
  TextStyle,
  TiptapImage,
  TiptapLink,
  TiptapUnderline,
  Twitter,
  UpdatedImage,
  UploadImagesPlugin,
  Youtube,
} from "novel";
import { Markdown } from "tiptap-markdown";
import { Table } from "@tiptap/extension-table";
import { TableHeader } from "@tiptap/extension-table-header";
import { TableRow } from "@tiptap/extension-table-row";
import { TableCell } from "@tiptap/extension-table-cell";
import { cx } from "class-variance-authority";
import { common, createLowlight } from "lowlight";
⋮----
addProseMirrorPlugins()

================
File: web/src/components/editor/image-upload.ts
================
import { createImageUpload } from "novel";
import { toast } from "sonner";
const onUpload = (file: File) =>

================
File: web/src/components/editor/slash-command.tsx
================
import {
  CheckSquare,
  Code,
  Heading1,
  Heading2,
  Heading3,
  List,
  ListOrdered,
  Text,
  TextQuote,
} from "lucide-react";
import { Command, createSuggestionItems, renderItems } from "novel";

================
File: web/src/components/magicui/aurora-text.tsx
================
import React, { memo } from "react";
interface AuroraTextProps {
  children: React.ReactNode;
  className?: string;
  colors?: string[];
  speed?: number;
}

================
File: web/src/components/magicui/bento-grid.tsx
================
import { ArrowRightIcon } from "@radix-ui/react-icons";
import type { ComponentPropsWithoutRef, ReactNode } from "react";
import { Button } from "~/components/ui/button";
import { cn } from "~/lib/utils";
interface BentoGridProps extends ComponentPropsWithoutRef<"div"> {
  children: ReactNode;
  className?: string;
}
interface BentoCardProps extends ComponentPropsWithoutRef<"div"> {
  name: string;
  className: string;
  background?: ReactNode;
  Icon: React.ElementType;
  description: string;
  href: string;
  cta: string;
}
const BentoGrid = (
⋮----
className=

================
File: web/src/components/magicui/flickering-grid.tsx
================
import { cn } from "~/lib/utils";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
interface FlickeringGridProps extends React.HTMLAttributes<HTMLDivElement> {
  squareSize?: number;
  gridGap?: number;
  flickerChance?: number;
  color?: string;
  width?: number;
  height?: number;
  className?: string;
  maxOpacity?: number;
}
export const FlickeringGrid: React.FC<FlickeringGridProps> = ({
  squareSize = 4,
  gridGap = 6,
  flickerChance = 0.3,
  color = "rgb(0, 0, 0)",
  width,
  height,
  className,
  maxOpacity = 0.3,
  ...props
}) =>
⋮----
const toRGBA = (color: string) =>
⋮----
const updateCanvasSize = () =>
⋮----
const animate = (time: number) =>
⋮----
className=

================
File: web/src/components/magicui/number-ticker.tsx
================
import { useInView, useMotionValue, useSpring } from "motion/react";
import { type ComponentPropsWithoutRef, useEffect, useRef } from "react";
import { cn } from "~/lib/utils";
interface NumberTickerProps extends ComponentPropsWithoutRef<"span"> {
  value: number;
  startValue?: number;
  direction?: "up" | "down";
  delay?: number;
  decimalPlaces?: number;
}
export function NumberTicker({
  value,
  startValue = 0,
  direction = "up",
  delay = 0,
  className,
  decimalPlaces = 0,
  ...props
}: NumberTickerProps)
⋮----
className=

================
File: web/src/components/magicui/shine-border.tsx
================
import { cn } from "~/lib/utils";
interface ShineBorderProps extends React.HTMLAttributes<HTMLDivElement> {
  borderWidth?: number;
  duration?: number;
  shineColor?: string | string[];
}
⋮----
className={cn(
        "pointer-events-none absolute inset-0 size-full rounded-[inherit] will-change-[background-position] motion-safe:animate-shine",
        className,
      )}
      {...props}
    />
  );

================
File: web/src/components/ui/icons/magic.tsx
================
export default function Magic(

================
File: web/src/components/ui/accordion.tsx
================
import { ChevronDownIcon } from "lucide-react"
import { cn } from "~/lib/utils"
function Accordion({
  ...props
}: React.ComponentProps<typeof AccordionPrimitive.Root>)
⋮----
function AccordionTrigger({
  className,
  children,
  ...props
}: React.ComponentProps<typeof AccordionPrimitive.Trigger>)
⋮----
className=
⋮----
function AccordionContent({
  className,
  children,
  ...props
}: React.ComponentProps<typeof AccordionPrimitive.Content>)

================
File: web/src/components/ui/badge.tsx
================
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "~/lib/utils"
⋮----
className=

================
File: web/src/components/ui/button.tsx
================
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
⋮----
import { cn } from "~/lib/utils";
⋮----
className={cn(
        buttonVariants({ variant, size, className }),
        "cursor-pointer active:scale-105",
      )}
      {...props}
    />
  );

================
File: web/src/components/ui/card.tsx
================
import { cn } from "~/lib/utils"

================
File: web/src/components/ui/checkbox.tsx
================
import { CheckIcon } from "lucide-react"
import { cn } from "~/lib/utils"
function Checkbox({
  className,
  ...props
}: React.ComponentProps<typeof CheckboxPrimitive.Root>)
⋮----
className=

================
File: web/src/components/ui/collapsible.tsx
================
function Collapsible({
  ...props
}: React.ComponentProps<typeof CollapsiblePrimitive.Root>)
function CollapsibleTrigger({
  ...props
}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleTrigger>)
function CollapsibleContent({
  ...props
}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleContent>)

================
File: web/src/components/ui/command.tsx
================
import { Command as CommandPrimitive } from "cmdk"
import { SearchIcon } from "lucide-react"
import { cn } from "~/lib/utils"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "~/components/ui/dialog"
⋮----
function CommandDialog({
  title = "Command Palette",
  description = "Search for a command to run...",
  children,
  ...props
}: React.ComponentProps<typeof Dialog> & {
  title?: string
  description?: string
})
⋮----
function CommandEmpty({
  ...props
}: React.ComponentProps<typeof CommandPrimitive.Empty>)

================
File: web/src/components/ui/dialog.tsx
================
import { XIcon } from "lucide-react"
import { cn } from "~/lib/utils"
function Dialog({
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Root>)
function DialogTrigger({
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Trigger>)
function DialogPortal({
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Portal>)
function DialogClose({
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Close>)
⋮----
function DialogContent({
  className,
  children,
  ...props
}: React.ComponentProps<typeof DialogPrimitive.Content>)
⋮----
className=

================
File: web/src/components/ui/dropdown-menu.tsx
================
import { CheckIcon, ChevronRightIcon, CircleIcon } from "lucide-react"
import { cn } from "~/lib/utils"
function DropdownMenu({
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>)
function DropdownMenuPortal({
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>)
function DropdownMenuTrigger({
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>)
⋮----
function DropdownMenuGroup({
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>)
⋮----
function DropdownMenuCheckboxItem({
  className,
  children,
  checked,
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>)
⋮----
className=
⋮----
function DropdownMenuRadioGroup({
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>)
function DropdownMenuRadioItem({
  className,
  children,
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>)
⋮----
function DropdownMenuSub({
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>)
function DropdownMenuSubTrigger({
  className,
  inset,
  children,
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {
  inset?: boolean
})

================
File: web/src/components/ui/form.tsx
================
import { Slot } from "@radix-ui/react-slot"
import {
  Controller,
  FormProvider,
  useFormContext,
  useFormState,
  type ControllerProps,
  type FieldPath,
  type FieldValues,
} from "react-hook-form"
import { cn } from "~/lib/utils"
import { Label } from "~/components/ui/label"
⋮----
type FormFieldContextValue<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> = {
  name: TName
}
⋮----
const FormField = <
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  ...props
}: ControllerProps<TFieldValues, TName>) =>
const useFormField = () =>
type FormItemContextValue = {
  id: string
}
⋮----
function FormItem(
⋮----
className=
⋮----
function FormLabel({
  className,
  ...props
}: React.ComponentProps<typeof LabelPrimitive.Root>)
function FormControl(
⋮----
function FormMessage(

================
File: web/src/components/ui/input.tsx
================
import { cn } from "~/lib/utils"
function Input(
⋮----
className=

================
File: web/src/components/ui/label.tsx
================
import { cn } from "~/lib/utils"
⋮----
className=

================
File: web/src/components/ui/popover.tsx
================
import { cn } from "~/lib/utils"
function Popover({
  ...props
}: React.ComponentProps<typeof PopoverPrimitive.Root>)
function PopoverTrigger({
  ...props
}: React.ComponentProps<typeof PopoverPrimitive.Trigger>)
⋮----
function PopoverAnchor({
  ...props
}: React.ComponentProps<typeof PopoverPrimitive.Anchor>)

================
File: web/src/components/ui/scroll-area.tsx
================
import { cn } from "~/lib/utils";
function ScrollArea({
  className,
  children,
  ref,
  ...props
}: React.ComponentProps<typeof ScrollAreaPrimitive.Root>)
⋮----
className=
⋮----
function ScrollBar({
  className,
  orientation = "vertical",
  ...props
}: React.ComponentProps<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>)

================
File: web/src/components/ui/select.tsx
================
import { CheckIcon, ChevronDownIcon, ChevronUpIcon } from "lucide-react"
import { cn } from "~/lib/utils"
function Select({
  ...props
}: React.ComponentProps<typeof SelectPrimitive.Root>)
function SelectGroup({
  ...props
}: React.ComponentProps<typeof SelectPrimitive.Group>)
function SelectValue({
  ...props
}: React.ComponentProps<typeof SelectPrimitive.Value>)
function SelectTrigger({
  className,
  size = "default",
  children,
  ...props
}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {
  size?: "sm" | "default"
})
⋮----
className=
⋮----
function SelectItem({
  className,
  children,
  ...props
}: React.ComponentProps<typeof SelectPrimitive.Item>)
⋮----
function SelectScrollUpButton({
  className,
  ...props
}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>)
function SelectScrollDownButton({
  className,
  ...props
}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>)

================
File: web/src/components/ui/separator.tsx
================
import { cn } from "~/lib/utils"
⋮----
className=

================
File: web/src/components/ui/sheet.tsx
================
import { XIcon } from "lucide-react"
import { cn } from "~/lib/utils"
function Sheet(
function SheetTrigger({
  ...props
}: React.ComponentProps<typeof SheetPrimitive.Trigger>)
function SheetClose({
  ...props
}: React.ComponentProps<typeof SheetPrimitive.Close>)
function SheetPortal({
  ...props
}: React.ComponentProps<typeof SheetPrimitive.Portal>)
⋮----
function SheetContent({
  className,
  children,
  side = "right",
  ...props
}: React.ComponentProps<typeof SheetPrimitive.Content> & {
  side?: "top" | "right" | "bottom" | "left"
})
⋮----
className=

================
File: web/src/components/ui/skeleton.tsx
================
import { cn } from "~/lib/utils"
⋮----
className=

================
File: web/src/components/ui/slider.tsx
================
import { cn } from "~/lib/utils"
⋮----
className=

================
File: web/src/components/ui/switch.tsx
================
import { cn } from "~/lib/utils"
⋮----
className=

================
File: web/src/components/ui/tabs.tsx
================
import { cn } from "~/lib/utils"
⋮----
className=

================
File: web/src/components/ui/textarea.tsx
================
import { cn } from "~/lib/utils"
⋮----
className=

================
File: web/src/components/ui/tooltip.tsx
================
import { cn } from "~/lib/utils";
function TooltipProvider({
  delayDuration = 0,
  ...props
}: React.ComponentProps<typeof TooltipPrimitive.Provider>)
function Tooltip({
  ...props
}: React.ComponentProps<typeof TooltipPrimitive.Root>)
function TooltipTrigger({
  ...props
}: React.ComponentProps<typeof TooltipPrimitive.Trigger>)
function TooltipContent({
  className,
  sideOffset = 0,
  children,
  ...props
}: React.ComponentProps<typeof TooltipPrimitive.Content>)
⋮----
className=

================
File: web/src/components/theme-provider.tsx
================
import { ThemeProvider as NextThemesProvider } from "next-themes";
⋮----
export function ThemeProvider({
  children,
  ...props
}: React.ComponentProps<typeof NextThemesProvider>)

================
File: web/src/core/api/podcast.ts
================
import { resolveServiceURL } from "./resolve-service-url";
export async function generatePodcast(content: string)

================
File: web/src/core/api/resolve-service-url.ts
================
import { env } from "~/env";
export function resolveServiceURL(path: string)

================
File: web/src/core/mcp/index.ts
================


================
File: web/src/core/mcp/schema.ts
================
import { z } from "zod";

================
File: web/src/core/mcp/types.ts
================
export interface MCPToolMetadata {
  name: string;
  description: string;
  inputSchema?: Record<string, unknown>;
}
export interface GenericMCPServerMetadata<T extends string> {
  name: string;
  transport: T;
  enabled: boolean;
  env?: Record<string, string>;
  tools: MCPToolMetadata[];
  createdAt: number;
  updatedAt: number;
}
export interface StdioMCPServerMetadata
  extends GenericMCPServerMetadata<"stdio"> {
  transport: "stdio";
  command: string;
  args?: string[];
}
export type SimpleStdioMCPServerMetadata = Omit<
  StdioMCPServerMetadata,
  "enabled" | "tools" | "createdAt" | "updatedAt"
>;
export interface SSEMCPServerMetadata extends GenericMCPServerMetadata<"sse"> {
  transport: "sse";
  url: string;
}
export type SimpleSSEMCPServerMetadata = Omit<
  SSEMCPServerMetadata,
  "enabled" | "tools" | "createdAt" | "updatedAt"
>;
export type MCPServerMetadata = StdioMCPServerMetadata | SSEMCPServerMetadata;
export type SimpleMCPServerMetadata =
  | SimpleStdioMCPServerMetadata
  | SimpleSSEMCPServerMetadata;

================
File: web/src/core/mcp/utils.ts
================
import { useSettingsStore } from "../store";
export function findMCPTool(name: string)

================
File: web/src/core/messages/index.ts
================


================
File: web/src/core/rehype/index.ts
================


================
File: web/src/core/rehype/rehype-split-words-into-spans.ts
================
import type { Element, Root, ElementContent } from "hast";
import { visit } from "unist-util-visit";
import type { BuildVisitor } from "unist-util-visit";
export function rehypeSplitWordsIntoSpans()

================
File: web/src/core/replay/get-replay-id.ts
================
export function extractReplayIdFromSearchParams(params: string)

================
File: web/src/core/replay/hooks.ts
================
import { useSearchParams } from "next/navigation";
import { useMemo } from "react";
import { env } from "~/env";
import { extractReplayIdFromSearchParams } from "./get-replay-id";
export function useReplay()

================
File: web/src/core/replay/index.ts
================


================
File: web/src/core/sse/fetch-stream.ts
================
import { type StreamEvent } from "./StreamEvent";
⋮----
function parseEvent(chunk: string)

================
File: web/src/core/sse/index.ts
================


================
File: web/src/core/sse/StreamEvent.ts
================
export interface StreamEvent {
  event: string;
  data: string;
}

================
File: web/src/core/store/index.ts
================


================
File: web/src/core/utils/deep-clone.ts
================
export function deepClone<T>(value: T): T

================
File: web/src/core/utils/index.ts
================


================
File: web/src/core/utils/markdown.ts
================
export function autoFixMarkdown(markdown: string): string
function autoCloseTrailingLink(markdown: string): string

================
File: web/src/core/utils/time.ts
================
export function sleep(ms: number)

================
File: web/src/hooks/use-intersection-observer.ts
================
import { useEffect, useRef, useState } from "react";
type State = {
  isIntersecting: boolean;
  entry?: IntersectionObserverEntry;
};
type UseIntersectionObserverOptions = {
  root?: Element | Document | null;
  rootMargin?: string;
  threshold?: number | number[];
  freezeOnceVisible?: boolean;
  onChange?: (
    isIntersecting: boolean,
    entry: IntersectionObserverEntry,
  ) => void;
  initialIsIntersecting?: boolean;
};
type IntersectionReturn = [
  (node?: Element | null) => void,
  boolean,
  IntersectionObserverEntry | undefined,
] & {
  ref: (node?: Element | null) => void;
  isIntersecting: boolean;
  entry?: IntersectionObserverEntry;
};
export function useIntersectionObserver({
  threshold = 0,
  root = null,
  rootMargin = "0%",
  freezeOnceVisible = false,
  initialIsIntersecting = false,
  onChange,
}: UseIntersectionObserverOptions =

================
File: web/src/hooks/use-mobile.ts
================
export function useIsMobile()
⋮----
const onChange = () =>

================
File: web/src/lib/utils.ts
================
import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
export function cn(...inputs: ClassValue[])

================
File: web/src/typings/md.d.ts
================


================
File: web/src/env.js
================
export const env = createEnv({
⋮----
NODE_ENV: z.enum(["development", "test", "production"]),
AMPLITUDE_API_KEY: z.string().optional(),
GITHUB_OAUTH_TOKEN: z.string().optional(),
⋮----
NEXT_PUBLIC_API_URL: z.string().optional(),
NEXT_PUBLIC_STATIC_WEBSITE_ONLY: z.boolean().optional(),

================
File: web/.env.example
================
# Since the ".env" file is gitignored, you can use the ".env.example" file to
# build a new ".env" file when you clone the repo. Keep this file up-to-date
# when you add new variables to `.env`.

# This file will be committed to version control, so make sure not to have any
# secrets in it. If you are cloning this repo, create a copy of this file named
# ".env" and populate it with your secrets.

# When adding additional environment variables, the schema in "/src/env.js"
# should be updated accordingly.

# Example:
# SERVERVAR="foo"
# NEXT_PUBLIC_CLIENTVAR="bar"

NEXT_PUBLIC_API_URL=http://localhost:8000/api

# Github
GITHUB_OAUTH_TOKEN=xxxx

================
File: web/.gitignore
================
# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# database
/prisma/db.sqlite
/prisma/db.sqlite-journal
db.sqlite

# next.js
/.next/
/out/
next-env.d.ts

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
# do not commit any .env files to git, except for the .env.example file. https://create.t3.gg/en/usage/env-variables#using-environment-variables
.env
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo

# idea files
.idea

================
File: web/components.json
================
{
  "$schema": "https://ui.shadcn.com/schema.json",
  "style": "new-york",
  "rsc": true,
  "tsx": true,
  "tailwind": {
    "config": "",
    "css": "src/styles/globals.css",
    "baseColor": "neutral",
    "cssVariables": true,
    "prefix": ""
  },
  "aliases": {
    "components": "~/components",
    "utils": "~/lib/utils",
    "ui": "~/components/ui",
    "lib": "~/lib",
    "hooks": "~/hooks"
  },
  "iconLibrary": "lucide"
}

================
File: web/docker-compose.yml
================
services:
  deer-flow-web:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        NEXT_PUBLIC_API_URL: ${NEXT_PUBLIC_API_URL}
    image: deer-flow-web
    ports:
      - "3000:3000"
    env_file:
      - .env

================
File: web/Dockerfile
================
##### DEPENDENCIES

FROM node:20-alpine AS deps
RUN apk add --no-cache libc6-compat openssl
WORKDIR /app


# Install dependencies based on the preferred package manager

COPY package.json yarn.lock* package-lock.json* pnpm-lock.yaml\* ./

RUN \
    if [ -f yarn.lock ]; then yarn --frozen-lockfile; \
    elif [ -f package-lock.json ]; then npm ci; \
    elif [ -f pnpm-lock.yaml ]; then npm install -g pnpm && pnpm i; \
    else echo "Lockfile not found." && exit 1; \
    fi

##### BUILDER

FROM node:20-alpine AS builder
WORKDIR /app
ARG NEXT_PUBLIC_API_URL
COPY --from=deps /app/node_modules ./node_modules
COPY . .

ENV NEXT_TELEMETRY_DISABLED=1

RUN \
    if [ -f yarn.lock ]; then SKIP_ENV_VALIDATION=1 yarn build; \
    elif [ -f package-lock.json ]; then SKIP_ENV_VALIDATION=1 npm run build; \
    elif [ -f pnpm-lock.yaml ]; then npm install -g pnpm && SKIP_ENV_VALIDATION=1 pnpm run build; \
    else echo "Lockfile not found." && exit 1; \
    fi

##### RUNNER

FROM gcr.io/distroless/nodejs20-debian12 AS runner
WORKDIR /app

ENV NODE_ENV=production

ENV NEXT_TELEMETRY_DISABLED=1

COPY --from=builder /app/next.config.js ./
COPY --from=builder /app/public ./public
COPY --from=builder /app/package.json ./package.json

COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static

EXPOSE 3000
ENV PORT=3000

CMD ["server.js"]

================
File: web/eslint.config.js
================
const compat = new FlatCompat({
⋮----
export default tseslint.config(
⋮----
...compat.extends("next/core-web-vitals"),

================
File: web/README.md
================
# 🦌 DeerFlow Web UI

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

> Originated from Open Source, give back to Open Source.

This is the web UI for [`DeerFlow`](https://github.com/bytedance/deer-flow).

## Quick Start

### Prerequisites

- [`DeerFlow`](https://github.com/bytedance/deer-flow)
- Node.js (v22.14.0+)
- pnpm (v10.6.2+) as package manager

### Configuration

Create a `.env` file in the project root and configure the following environment variables:

- `NEXT_PUBLIC_API_URL`: The URL of the deer-flow API.

It's always a good idea to start with the given example file, and edit the `.env` file with your own values:

```bash
cp .env.example .env
```

## How to Install

DeerFlow Web UI uses `pnpm` as its package manager.
To install the dependencies, run:

```bash
cd web
pnpm install
```

## How to Run in Development Mode

> [!NOTE]
> Ensure the Python API service is running before starting the web UI.

Start the web UI development server:

```bash
cd web
pnpm dev
```

By default, the web UI will be available at `http://localhost:3000`.

You can set the `NEXT_PUBLIC_API_URL` environment variable if you're using a different host or location.

```ini
# .env
NEXT_PUBLIC_API_URL=http://localhost:8000/api
```

## Docker

You can also run this project with Docker.

First, you need read the [configuration](#configuration) below. Make sure `.env` file is ready.

Second, to build a Docker image of your own web server:

```bash
docker build --build-arg NEXT_PUBLIC_API_URL=YOUR_DEER-FLOW_API -t deer-flow-web .
```

Final, start up a docker container running the web server:

```bash
# Replace deer-flow-web-app with your preferred container name
docker run -d -t -p 3000:3000 --env-file .env --name deer-flow-web-app deer-flow-web

# stop the server
docker stop deer-flow-web-app
```

### Docker Compose

You can also setup this project with the docker compose:

```bash
# building docker image
docker compose build

# start the server
docker compose up
```

## License

This project is open source and available under the [MIT License](../LICENSE).

## Acknowledgments

We extend our heartfelt gratitude to the open source community for their invaluable contributions.
DeerFlow is built upon the foundation of these outstanding projects:

In particular, we want to express our deep appreciation for:

- [Next.js](https://nextjs.org/) for their exceptional framework
- [Shadcn](https://ui.shadcn.com/) for their minimalistic components that powers our UI
- [Zustand](https://zustand.docs.pmnd.rs/) for their stunning state management
- [Framer Motion](https://www.framer.com/motion/) for their amazing animation library
- [React Markdown](https://www.npmjs.com/package/react-markdown) for their exceptional markdown rendering and customizability
- Last but not least, special thanks to [SToneX](https://github.com/stonexer) for his great contribution for [token-by-token visual effect](./src/core/rehype/rehype-split-words-into-spans.ts)

These outstanding projects form the backbone of DeerFlow and exemplify the transformative power of open source collaboration.

================
File: web/tsconfig.json
================
{
  "compilerOptions": {
    /* Base Options: */
    "esModuleInterop": true,
    "skipLibCheck": true,
    "target": "es2022",
    "allowJs": true,
    "resolveJsonModule": true,
    "moduleDetection": "force",
    "isolatedModules": true,
    "verbatimModuleSyntax": true,

    /* Strictness */
    "strict": true,
    "noUncheckedIndexedAccess": true,
    "checkJs": true,

    /* Bundled projects */
    "lib": ["dom", "dom.iterable", "ES2022"],
    "noEmit": true,
    "module": "ESNext",
    "moduleResolution": "Bundler",
    "jsx": "preserve",
    "plugins": [{ "name": "next" }],
    "incremental": true,

    /* Path Aliases */
    "baseUrl": ".",
    "paths": {
      "~/*": ["./src/*"]
    }
  },
  "include": [
    "next-env.d.ts",
    "**/*.ts",
    "**/*.tsx",
    "**/*.cjs",
    "**/*.js",
    ".next/types/**/*.ts"
  ],
  "exclude": ["node_modules"]
}

================
File: web/docs/implementation-summary.md
================
# 深度思考块功能实现总结

## 🎯 实现的功能

### 核心特性
1. **智能展示逻辑**: 深度思考过程初始展开，计划内容开始时自动折叠
2. **分阶段显示**: 思考阶段只显示思考块，思考结束后才显示计划卡片
3. **动态主题**: 思考阶段使用蓝色主题，完成后切换为默认主题
4. **流式支持**: 实时展示推理内容的流式传输
5. **优雅交互**: 平滑的动画效果和状态切换

### 交互流程
```
用户发送问题 (启用深度思考)
    ↓
开始接收 reasoning_content
    ↓
思考块自动展开 + primary 主题 + 加载动画
    ↓
推理内容流式更新
    ↓
开始接收 content (计划内容)
    ↓
思考块自动折叠 + 主题切换
    ↓
计划卡片优雅出现 (动画效果)
    ↓
计划内容保持流式更新 (标题→思路→步骤)
    ↓
完成 (用户可手动展开思考块)
```

## 🔧 技术实现

### 数据结构扩展
- `Message` 接口添加 `reasoningContent` 和 `reasoningContentChunks` 字段
- `MessageChunkEvent` 接口添加 `reasoning_content` 字段
- 消息合并逻辑支持推理内容的流式处理

### 组件架构
- `ThoughtBlock`: 可折叠的思考块组件
- `PlanCard`: 更新后的计划卡片，集成思考块
- 智能状态管理和条件渲染

### 状态管理
```typescript
// 关键状态逻辑
const hasMainContent = message.content && message.content.trim() !== "";
const isThinking = reasoningContent && !hasMainContent;
const shouldShowPlan = hasMainContent; // 有内容就显示，保持流式效果
```

### 自动折叠逻辑
```typescript
React.useEffect(() => {
  if (hasMainContent && !hasAutoCollapsed) {
    setIsOpen(false);
    setHasAutoCollapsed(true);
  }
}, [hasMainContent, hasAutoCollapsed]);
```

## 🎨 视觉设计

### 统一设计语言
- **字体系统**: 使用 `font-semibold` 与 CardTitle 保持一致
- **圆角规范**: 采用 `rounded-xl` 与其他卡片组件统一
- **间距标准**: 使用 `px-6 py-4` 内边距，`mb-6` 外边距
- **图标尺寸**: 18px 大脑图标，与文字比例协调

### 思考阶段样式
- Primary 主题色边框和背景
- Primary 色图标和文字
- 标准边框样式
- 加载动画

### 完成阶段样式
- 默认 border 和 card 背景
- muted-foreground 图标
- 80% 透明度文字
- 静态图标

### 动画效果
- 展开/折叠动画
- 主题切换过渡
- 颜色变化动画

## 📁 文件更改

### 核心文件
1. `web/src/core/messages/types.ts` - 消息类型扩展
2. `web/src/core/api/types.ts` - API 事件类型扩展
3. `web/src/core/messages/merge-message.ts` - 消息合并逻辑
4. `web/src/core/store/store.ts` - 状态管理更新
5. `web/src/app/chat/components/message-list-view.tsx` - 主要组件实现

### 测试和文档
1. `web/public/mock/reasoning-example.txt` - 测试数据
2. `web/docs/thought-block-feature.md` - 功能文档
3. `web/docs/testing-thought-block.md` - 测试指南
4. `web/docs/interaction-flow-test.md` - 交互流程测试

## 🧪 测试方法

### 快速测试
```
访问: http://localhost:3000?mock=reasoning-example
发送任意消息，观察交互流程
```

### 完整测试
1. 启用深度思考模式
2. 配置 reasoning 模型
3. 发送复杂问题
4. 验证完整交互流程

## 🔄 兼容性

- ✅ 向后兼容：无推理内容时正常显示
- ✅ 渐进增强：功能仅在有推理内容时激活
- ✅ 优雅降级：推理内容为空时不显示思考块

## 🚀 使用建议

1. **启用深度思考**: 点击"Deep Thinking"按钮
2. **观察流程**: 注意思考块的自动展开和折叠
3. **手动控制**: 可随时点击思考块标题栏控制展开/折叠
4. **查看推理**: 展开思考块查看完整的推理过程

这个实现完全满足了用户的需求，提供了直观、流畅的深度思考过程展示体验。

================
File: web/docs/interaction-flow-test.md
================
# 思考块交互流程测试

## 测试场景

### 场景 1: 完整的深度思考流程

**步骤**:
1. 启用深度思考模式
2. 发送问题："什么是 vibe coding？"
3. 观察交互流程

**预期行为**:

#### 阶段 1: 深度思考开始
- ✅ 思考块立即出现并展开
- ✅ 使用蓝色主题（边框、背景、图标、文字）
- ✅ 显示加载动画
- ✅ 不显示计划卡片
- ✅ 推理内容实时流式更新

#### 阶段 2: 思考过程中
- ✅ 思考块保持展开状态
- ✅ 蓝色主题持续显示
- ✅ 推理内容持续增加
- ✅ 加载动画持续显示
- ✅ 计划卡片仍然不显示

#### 阶段 3: 开始接收计划内容
- ✅ 思考块自动折叠
- ✅ 主题从 primary 切换为默认
- ✅ 加载动画消失
- ✅ 计划卡片以优雅动画出现（opacity: 0→1, y: 20→0）
- ✅ 计划内容保持流式更新效果

#### 阶段 4: 计划流式输出
- ✅ 标题逐步显示
- ✅ 思路内容流式更新
- ✅ 步骤列表逐项显示
- ✅ 每个步骤的标题和描述分别流式渲染

#### 阶段 5: 计划完成
- ✅ 思考块保持折叠状态
- ✅ 计划卡片完全显示
- ✅ 用户可手动展开思考块查看推理过程

### 场景 2: 手动交互测试

**步骤**:
1. 在思考完成后，手动点击思考块
2. 验证展开/折叠功能

**预期行为**:
- ✅ 点击可正常展开/折叠
- ✅ 动画效果流畅
- ✅ 内容完整显示
- ✅ 不影响计划卡片显示

### 场景 3: 边界情况测试

#### 3.1 只有推理内容，没有计划内容
**预期**: 思考块保持展开，不显示计划卡片

#### 3.2 没有推理内容，只有计划内容
**预期**: 不显示思考块，直接显示计划卡片

#### 3.3 推理内容为空
**预期**: 不显示思考块，直接显示计划卡片

## 验证要点

### 视觉效果
- [ ] Primary 主题色在思考阶段正确显示
- [ ] 主题切换动画流畅
- [ ] 字体权重与 CardTitle 保持一致 (`font-semibold`)
- [ ] 圆角设计与其他卡片统一 (`rounded-xl`)
- [ ] 图标尺寸和颜色正确变化 (18px, primary/muted-foreground)
- [ ] 内边距与设计系统一致 (`px-6 py-4`)
- [ ] 整体视觉层次与页面协调

### 交互逻辑
- [ ] 自动展开/折叠时机正确
- [ ] 手动展开/折叠功能正常
- [ ] 计划卡片显示时机正确
- [ ] 加载动画显示时机正确

### 内容渲染
- [ ] 推理内容正确流式更新
- [ ] Markdown 格式正确渲染
- [ ] 中文内容正确显示
- [ ] 内容不丢失或重复

### 性能表现
- [ ] 动画流畅，无卡顿
- [ ] 内存使用正常
- [ ] 组件重新渲染次数合理

## 故障排除

### 思考块不自动折叠
1. 检查 `hasMainContent` 逻辑
2. 验证 `useEffect` 依赖项
3. 确认 `hasAutoCollapsed` 状态管理

### 计划卡片显示时机错误
1. 检查 `shouldShowPlan` 计算逻辑
2. 验证 `isThinking` 状态判断
3. 确认消息内容解析正确

### 主题切换异常
1. 检查 `isStreaming` 状态
2. 验证 CSS 类名应用
3. 确认条件渲染逻辑

================
File: web/docs/streaming-improvements.md
================
# 流式输出优化改进

## 🎯 改进目标

确保在深度思考结束后，plan block 保持流式输出效果，提供更流畅丝滑的用户体验。

## 🔧 技术改进

### 状态逻辑优化

**之前的逻辑**:
```typescript
const isThinking = reasoningContent && (!hasMainContent || message.isStreaming);
const shouldShowPlan = hasMainContent && !isThinking;
```

**优化后的逻辑**:
```typescript
const isThinking = reasoningContent && !hasMainContent;
const shouldShowPlan = hasMainContent; // 简化逻辑，有内容就显示
```

### 关键改进点

1. **简化显示逻辑**: 只要有主要内容就显示 plan，不再依赖思考状态
2. **保持流式状态**: plan 组件的 `animated` 属性直接使用 `message.isStreaming`
3. **优雅入场动画**: 添加 motion.div 包装，提供平滑的出现效果

## 🎨 用户体验提升

### 流式输出效果

#### 思考阶段
- ✅ 推理内容实时流式更新
- ✅ 思考块保持展开状态
- ✅ Primary 主题色高亮显示

#### 计划阶段
- ✅ 计划卡片优雅出现（300ms 动画）
- ✅ 标题内容流式渲染
- ✅ 思路内容流式更新
- ✅ 步骤列表逐项显示
- ✅ 每个步骤的标题和描述分别流式渲染

### 动画效果

#### 计划卡片入场动画
```typescript
<motion.div
  initial={{ opacity: 0, y: 20 }}
  animate={{ opacity: 1, y: 0 }}
  transition={{ duration: 0.3, ease: "easeOut" }}
>
```

#### 流式文本动画
- 所有 Markdown 组件都使用 `animated={message.isStreaming}`
- 确保文本逐字符或逐词显示效果

## 📊 性能优化

### 渲染优化
- **减少重新渲染**: 简化状态逻辑，减少不必要的组件重新挂载
- **保持组件实例**: plan 组件一旦出现就保持存在，避免重新创建
- **流式状态传递**: 直接使用消息的流式状态，避免额外的状态计算

### 内存优化
- **组件复用**: 避免频繁的组件销毁和重建
- **状态管理**: 简化状态依赖，减少内存占用

## 🧪 测试验证

### 流式效果验证
1. **思考阶段**: 推理内容应该逐步显示
2. **过渡阶段**: 计划卡片应该平滑出现
3. **计划阶段**: 所有计划内容应该保持流式效果

### 动画效果验证
1. **入场动画**: 计划卡片应该从下方滑入并淡入
2. **文本动画**: 所有文本内容应该有打字机效果
3. **状态切换**: 思考块折叠应该平滑自然

### 性能验证
1. **渲染次数**: 检查组件重新渲染频率
2. **内存使用**: 监控内存占用情况
3. **动画流畅度**: 确保 60fps 的动画效果

## 📝 使用示例

### 完整交互流程
```
1. 用户发送问题 (启用深度思考)
   ↓
2. 思考块展开，推理内容流式显示
   ↓
3. 开始接收计划内容
   ↓
4. 思考块自动折叠
   ↓
5. 计划卡片优雅出现 (动画效果)
   ↓
6. 计划内容流式渲染:
   - 标题逐步显示
   - 思路内容流式更新
   - 步骤列表逐项显示
   ↓
7. 完成，用户可查看完整内容
```

## 🔄 兼容性

- ✅ **向后兼容**: 不影响现有的非深度思考模式
- ✅ **渐进增强**: 功能仅在有推理内容时激活
- ✅ **优雅降级**: 在不支持的环境中正常显示

## 🚀 效果总结

这次优化显著提升了用户体验：

1. **更流畅的过渡**: 从思考到计划的切换更加自然
2. **保持流式效果**: 计划内容保持了原有的流式输出特性
3. **视觉连贯性**: 整个过程的视觉效果更加连贯统一
4. **性能提升**: 减少了不必要的组件重新渲染

用户现在可以享受到完整的流式体验，从深度思考到计划展示都保持了一致的流畅感。

================
File: web/docs/testing-thought-block.md
================
# 测试思考块功能

## 快速测试

### 方法 1: 使用模拟数据

1. 在浏览器中访问应用并添加 `?mock=reasoning-example` 参数
2. 发送任意消息
3. 观察计划卡片上方是否出现思考块

### 方法 2: 启用深度思考模式

1. 确保配置了 reasoning 模型（如 DeepSeek R1）
2. 在聊天界面点击"Deep Thinking"按钮
3. 发送一个需要规划的问题
4. 观察是否出现思考块

## 预期行为

### 思考块外观
- 深度思考开始时自动展开显示
- 思考阶段使用 primary 主题色（边框、背景、文字、图标）
- 带有 18px 大脑图标和"深度思考过程"标题
- 使用 `font-semibold` 字体权重，与 CardTitle 保持一致
- `rounded-xl` 圆角设计，与其他卡片组件统一
- 标准的 `px-6 py-4` 内边距

### 交互行为
- 思考阶段：自动展开，蓝色高亮，显示加载动画
- 计划阶段：自动折叠，切换为默认主题
- 用户可随时手动展开/折叠
- 平滑的展开/折叠动画和主题切换

### 分阶段显示
- 思考阶段：只显示思考块，不显示计划卡片
- 计划阶段：思考块折叠，显示完整计划卡片

### 内容渲染
- 支持 Markdown 格式
- 中文内容正确显示
- 保持原有的换行和格式

## 故障排除

### 思考块不显示
1. 检查消息是否包含 `reasoningContent` 字段
2. 确认 `reasoning_content` 事件是否正确处理
3. 验证消息合并逻辑是否正常工作

### 内容显示异常
1. 检查 Markdown 渲染是否正常
2. 确认 CSS 样式是否正确加载
3. 验证动画效果是否启用

### 流式传输问题
1. 检查 WebSocket 连接状态
2. 确认事件流格式是否正确
3. 验证消息更新逻辑

## 开发调试

### 控制台检查
```javascript
// 检查消息对象
const messages = useStore.getState().messages;
const lastMessage = Array.from(messages.values()).pop();
console.log('Reasoning content:', lastMessage?.reasoningContent);
```

### 网络面板
- 查看 SSE 事件流
- 确认 `reasoning_content` 字段存在
- 检查事件格式是否正确

### React DevTools
- 检查 ThoughtBlock 组件状态
- 验证 props 传递是否正确
- 观察组件重新渲染情况

================
File: web/docs/thought-block-design-system.md
================
# 思考块设计系统规范

## 🎯 设计目标

确保思考块组件与整个应用的设计语言保持完全一致，提供统一的用户体验。

## 📐 设计规范

### 字体系统
```css
/* 标题字体 - 与 CardTitle 保持一致 */
font-weight: 600; /* font-semibold */
line-height: 1; /* leading-none */
```

### 尺寸规范
```css
/* 图标尺寸 */
icon-size: 18px; /* 与文字比例协调 */

/* 内边距 */
padding: 1.5rem; /* px-6 py-4 */

/* 外边距 */
margin-bottom: 1.5rem; /* mb-6 */

/* 圆角 */
border-radius: 0.75rem; /* rounded-xl */
```

### 颜色系统

#### 思考阶段（活跃状态）
```css
/* 边框和背景 */
border-color: hsl(var(--primary) / 0.2);
background-color: hsl(var(--primary) / 0.05);

/* 图标和文字 */
color: hsl(var(--primary));

/* 阴影 */
box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
```

#### 完成阶段（静态状态）
```css
/* 边框和背景 */
border-color: hsl(var(--border));
background-color: hsl(var(--card));

/* 图标 */
color: hsl(var(--muted-foreground));

/* 文字 */
color: hsl(var(--foreground));
```

#### 内容区域
```css
/* 思考阶段 */
.prose-primary {
  color: hsl(var(--primary));
}

/* 完成阶段 */
.opacity-80 {
  opacity: 0.8;
}
```

### 交互状态
```css
/* 悬停状态 */
.hover\:bg-accent:hover {
  background-color: hsl(var(--accent));
}

.hover\:text-accent-foreground:hover {
  color: hsl(var(--accent-foreground));
}
```

## 🔄 状态变化

### 状态映射
| 状态 | 边框 | 背景 | 图标颜色 | 文字颜色 | 阴影 |
|------|------|------|----------|----------|------|
| 思考中 | primary/20 | primary/5 | primary | primary | 有 |
| 已完成 | border | card | muted-foreground | foreground | 无 |

### 动画过渡
```css
transition: all 200ms ease-in-out;
```

## 📱 响应式设计

### 间距适配
- 移动端：保持相同的内边距比例
- 桌面端：标准的 `px-6 py-4` 内边距

### 字体适配
- 所有设备：保持 `font-semibold` 字体权重
- 图标尺寸：固定 18px，确保清晰度

## 🎨 与现有组件的对比

### CardTitle 对比
| 属性 | CardTitle | ThoughtBlock |
|------|-----------|--------------|
| 字体权重 | font-semibold | font-semibold ✅ |
| 行高 | leading-none | leading-none ✅ |
| 颜色 | foreground | primary/foreground |

### Card 对比
| 属性 | Card | ThoughtBlock |
|------|------|--------------|
| 圆角 | rounded-lg | rounded-xl |
| 边框 | border | border ✅ |
| 背景 | card | card/primary ✅ |

### Button 对比
| 属性 | Button | ThoughtBlock Trigger |
|------|--------|---------------------|
| 内边距 | 标准 | px-6 py-4 ✅ |
| 悬停 | hover:bg-accent | hover:bg-accent ✅ |
| 圆角 | rounded-md | rounded-xl |

## ✅ 设计检查清单

### 视觉一致性
- [ ] 字体权重与 CardTitle 一致
- [ ] 圆角设计与卡片组件统一
- [ ] 颜色使用 CSS 变量系统
- [ ] 间距符合设计规范

### 交互一致性
- [ ] 悬停状态与 Button 组件一致
- [ ] 过渡动画时长统一（200ms）
- [ ] 状态变化平滑自然

### 可访问性
- [ ] 颜色对比度符合 WCAG 标准
- [ ] 图标尺寸适合点击/触摸
- [ ] 状态变化有明确的视觉反馈

## 🔧 实现要点

1. **使用设计系统变量**: 所有颜色都使用 CSS 变量，确保主题切换正常
2. **保持组件一致性**: 与现有 Card、Button 组件的样式保持一致
3. **响应式友好**: 在不同设备上都有良好的显示效果
4. **性能优化**: 使用 CSS 过渡而非 JavaScript 动画

这个设计系统确保了思考块组件与整个应用的视觉语言完全统一，提供了一致的用户体验。

================
File: web/docs/thought-block-feature.md
================
# 思考块功能 (Thought Block Feature)

## 概述

思考块功能允许在计划卡片之前展示 AI 的深度思考过程，以可折叠的方式呈现推理内容。这个功能特别适用于启用深度思考模式时的场景。

## 功能特性

- **智能展示逻辑**: 深度思考过程初始展开，当开始接收计划内容时自动折叠
- **分阶段显示**: 思考阶段只显示思考块，思考结束后才显示计划卡片
- **流式支持**: 支持推理内容的实时流式展示
- **视觉状态反馈**: 思考阶段使用蓝色主题突出显示
- **优雅的动画**: 包含平滑的展开/折叠动画效果
- **响应式设计**: 适配不同屏幕尺寸

## 技术实现

### 数据结构更新

1. **Message 类型扩展**:
   ```typescript
   export interface Message {
     // ... 其他字段
     reasoningContent?: string;
     reasoningContentChunks?: string[];
   }
   ```

2. **API 事件类型扩展**:
   ```typescript
   export interface MessageChunkEvent {
     // ... 其他字段
     reasoning_content?: string;
   }
   ```

### 组件结构

- **ThoughtBlock**: 主要的思考块组件
  - 使用 Radix UI 的 Collapsible 组件
  - 支持流式内容展示
  - 包含加载动画和状态指示

- **PlanCard**: 更新后的计划卡片
  - 在计划内容之前展示思考块
  - 自动检测是否有推理内容

### 消息处理

消息合并逻辑已更新以支持 `reasoning_content` 字段的流式处理：

```typescript
function mergeTextMessage(message: Message, event: MessageChunkEvent) {
  // 处理常规内容
  if (event.data.content) {
    message.content += event.data.content;
    message.contentChunks.push(event.data.content);
  }
  
  // 处理推理内容
  if (event.data.reasoning_content) {
    message.reasoningContent = (message.reasoningContent || "") + event.data.reasoning_content;
    message.reasoningContentChunks = message.reasoningContentChunks || [];
    message.reasoningContentChunks.push(event.data.reasoning_content);
  }
}
```

## 使用方法

### 启用深度思考模式

1. 在聊天界面中，点击"Deep Thinking"按钮
2. 确保配置了支持推理的模型
3. 发送消息后，如果有推理内容，会在计划卡片上方显示思考块

### 查看推理过程

1. 深度思考开始时，思考块自动展开显示
2. 思考阶段使用 primary 主题色，突出显示正在进行的推理过程
3. 推理内容支持 Markdown 格式渲染，实时流式更新
4. 在流式传输过程中会显示加载动画
5. 当开始接收计划内容时，思考块自动折叠
6. 计划卡片以优雅的动画效果出现
7. 计划内容保持流式输出效果，逐步显示标题、思路和步骤
8. 用户可以随时点击思考块标题栏手动展开/折叠

## 样式特性

- **统一设计语言**: 与页面整体设计风格保持一致
- **字体层次**: 使用与 CardTitle 相同的 `font-semibold` 字体权重
- **圆角设计**: 采用 `rounded-xl` 与其他卡片组件保持一致
- **间距规范**: 使用标准的 `px-6 py-4` 内边距
- **动态主题**: 思考阶段使用 primary 色彩系统
- **图标尺寸**: 18px 图标尺寸，与文字比例协调
- **状态反馈**: 流式传输时显示加载动画和主题色高亮
- **交互反馈**: 标准的 hover 和 focus 状态
- **平滑过渡**: 所有状态变化都有平滑的过渡动画

## 测试数据

可以使用 `/mock/reasoning-example.txt` 文件测试思考块功能，该文件包含了模拟的推理内容和计划数据。

## 兼容性

- 向后兼容：没有推理内容的消息不会显示思考块
- 渐进增强：功能仅在有推理内容时激活
- 优雅降级：如果推理内容为空，组件不会渲染

================
File: web/src/app/chat/components/conversation-starter.tsx
================
import { motion } from "framer-motion";
import { useTranslations } from "next-intl";
import { cn } from "~/lib/utils";
import { Welcome } from "./welcome";

================
File: web/src/app/chat/components/research-report-block.tsx
================
import { useCallback, useRef } from "react";
import { LoadingAnimation } from "~/components/deer-flow/loading-animation";
import { Markdown } from "~/components/deer-flow/markdown";
import ReportEditor from "~/components/editor";
import { useReplay } from "~/core/replay";
import { useMessage, useStore } from "~/core/store";
import { cn } from "~/lib/utils";
⋮----
// TODO: scroll to top when completed, but it's not working
// useEffect(() => {
//   if (isCompleted && contentRef.current) {
//     setTimeout(() => {
//       contentRef
//         .current!.closest("[data-radix-scroll-area-viewport]")
//         ?.scrollTo({
//           top: 0,
//           behavior: "smooth",
⋮----
<div ref=

================
File: web/src/app/chat/components/site-header.tsx
================
import { StarFilledIcon, GitHubLogoIcon } from "@radix-ui/react-icons";
import Link from "next/link";
import { useTranslations } from 'next-intl';
import { LanguageSwitcher } from "~/components/deer-flow/language-switcher";
import { NumberTicker } from "~/components/magicui/number-ticker";
import { Button } from "~/components/ui/button";
import { env } from "~/env";

================
File: web/src/app/chat/components/welcome.tsx
================
import { motion } from "framer-motion";
import { useTranslations } from "next-intl";
import { cn } from "~/lib/utils";
⋮----
className=

================
File: web/src/app/chat/page.tsx
================
import { GithubOutlined } from "@ant-design/icons";
import dynamic from "next/dynamic";
import Link from "next/link";
import { useTranslations } from "next-intl";
import { Suspense } from "react";
import { FileText } from "lucide-react";
import { Button } from "~/components/ui/button";
import { Logo } from "../../components/deer-flow/logo";
import { ThemeToggle } from "../../components/deer-flow/theme-toggle";
import { Tooltip } from "../../components/deer-flow/tooltip";
import { SettingsDialog } from "../settings/dialogs/settings-dialog";
⋮----
export default function HomePage()
⋮----
<Tooltip title=

================
File: web/src/app/landing/components/jumbotron.tsx
================
import { GithubFilled } from "@ant-design/icons";
import { ChevronRight } from "lucide-react";
import Link from "next/link";
import { useTranslations } from 'next-intl';
import { AuroraText } from "~/components/magicui/aurora-text";
import { FlickeringGrid } from "~/components/magicui/flickering-grid";
import { Button } from "~/components/ui/button";
import { env } from "~/env";

================
File: web/src/app/landing/components/multi-agent-visualization.tsx
================
import {
  ReactFlow,
  Background,
  Handle,
  Position,
  type Edge,
  type ReactFlowInstance,
} from "@xyflow/react";
import {
  Play,
  type LucideIcon,
  ChevronRight,
  ChevronLeft,
  Pause,
  Fullscreen,
  Minimize,
} from "lucide-react";
⋮----
import { useTranslations } from "next-intl";
import { useCallback, useRef, useState } from "react";
import { Tooltip } from "~/components/deer-flow/tooltip";
import { ShineBorder } from "~/components/magicui/shine-border";
import { Button } from "~/components/ui/button";
import { Slider } from "~/components/ui/slider";
import { useIntersectionObserver } from "~/hooks/use-intersection-observer";
import { cn } from "~/lib/utils";
import { playbook, type GraphNode } from "../store";
import {
  activateStep,
  nextStep,
  play,
  prevStep,
  togglePlay,
  useMAVStore,
} from "../store/mav-store";
⋮----
className=
⋮----
<Tooltip title=

================
File: web/src/app/landing/sections/case-study-section.tsx
================
import { Bike, Building, Film, Github, Ham, Home, Pizza } from "lucide-react";
import { Bot } from "lucide-react";
import { useTranslations } from "next-intl";
import { BentoCard } from "~/components/magicui/bento-grid";
import { SectionHeader } from "../components/section-header";

================
File: web/src/app/landing/sections/core-features-section.tsx
================
import {
  Bird,
  Microscope,
  Podcast,
  Usb,
  User,
  type LucideProps,
} from "lucide-react";
import { useTranslations } from "next-intl";
import type { ForwardRefExoticComponent, RefAttributes } from "react";
import { BentoCard, BentoGrid } from "~/components/magicui/bento-grid";
import { SectionHeader } from "../components/section-header";
type FeatureIcon = {
  Icon: ForwardRefExoticComponent<
    Omit<LucideProps, "ref"> & RefAttributes<SVGSVGElement>
  >;
  href: string;
  className: string;
};

================
File: web/src/app/landing/sections/join-community-section.tsx
================
import { GithubFilled } from "@ant-design/icons";
import Link from "next/link";
import { useTranslations } from "next-intl";
import { AuroraText } from "~/components/magicui/aurora-text";
import { Button } from "~/components/ui/button";
import { SectionHeader } from "../components/section-header";

================
File: web/src/app/landing/sections/multi-agent-section.tsx
================
import { useTranslations } from "next-intl";
import { MultiAgentVisualization } from "../components/multi-agent-visualization";
import { SectionHeader } from "../components/section-header";

================
File: web/src/app/settings/dialogs/settings-dialog.tsx
================
import { Settings } from "lucide-react";
import { useTranslations } from 'next-intl';
import { useCallback, useEffect, useMemo, useState } from "react";
import { Tooltip } from "~/components/deer-flow/tooltip";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "~/components/ui/dialog";
import { Tabs, TabsContent } from "~/components/ui/tabs";
import { useReplay } from "~/core/replay";
import {
  type SettingsState,
  changeSettings,
  saveSettings,
  useSettingsStore,
} from "~/core/store";
import { cn } from "~/lib/utils";
import { SETTINGS_TABS } from "../tabs";
⋮----
<Tooltip title=
⋮----
className=

================
File: web/src/app/settings/tabs/about-en.md
================
# 🦌 [About DeerFlow](https://github.com/bytedance/deer-flow)

> **From Open Source, Back to Open Source**

**DeerFlow** (**D**eep **E**xploration and **E**fficient **R**esearch **Flow**) is a community-driven AI automation framework inspired by the remarkable contributions of the open source community. Our mission is to seamlessly integrate language models with specialized tools for tasks such as web search, crawling, and Python code execution—all while giving back to the community that made this innovation possible.

---

## 🌟 GitHub Repository

Explore DeerFlow on GitHub: [github.com/bytedance/deer-flow](https://github.com/bytedance/deer-flow)

---

## 📜 License

DeerFlow is proudly open source and distributed under the **MIT License**.

---

## 🙌 Acknowledgments

We extend our heartfelt gratitude to the open source projects and contributors who have made DeerFlow a reality. We truly stand on the shoulders of giants.

### Core Frameworks
- **[LangChain](https://github.com/langchain-ai/langchain)**: A phenomenal framework that powers our LLM interactions and chains.
- **[LangGraph](https://github.com/langchain-ai/langgraph)**: Enabling sophisticated multi-agent orchestration.
- **[Next.js](https://nextjs.org/)**: A cutting-edge framework for building web applications.

### UI Libraries
- **[Shadcn](https://ui.shadcn.com/)**: Minimalistic components that power our UI.
- **[Zustand](https://zustand.docs.pmnd.rs/)**: A stunning state management library.
- **[Framer Motion](https://www.framer.com/motion/)**: An amazing animation library.
- **[React Markdown](https://www.npmjs.com/package/react-markdown)**: Exceptional markdown rendering with customizability.
- **[SToneX](https://github.com/stonexer)**: For his invaluable contribution to token-by-token visual effects.

These outstanding projects form the backbone of DeerFlow and exemplify the transformative power of open source collaboration.

### Special Thanks
Finally, we want to express our heartfelt gratitude to the core authors of `DeerFlow`:

- **[Daniel Walnut](https://github.com/hetaoBackend/)**
- **[Henry Li](https://github.com/magiccube/)**

Without their vision, passion and dedication, `DeerFlow` would not be what it is today.

================
File: web/src/app/settings/tabs/about-zh.md
================
# 🦌 [关于 DeerFlow](https://github.com/bytedance/deer-flow)

> **源于开源，回馈开源**

**DeerFlow**（**深度探索**和**高效研究**流程）是一个由社区驱动的 AI 自动化框架，受到开源社区卓越贡献的启发。我们的使命是将语言模型与专业工具无缝集成，用于网络搜索、爬取和 Python 代码执行等任务——同时回馈使这种创新成为可能的社区。

---

## 🌟 GitHub 仓库

在 GitHub 上探索 DeerFlow：[github.com/bytedance/deer-flow](https://github.com/bytedance/deer-flow)

---

## 📜 软件许可证

DeerFlow 作为开源项目，在 **MIT 许可证** 下分发。

---

## 🙌 致谢

我们衷心感谢使 DeerFlow 成为现实的开源项目和贡献者。我们真正站在巨人的肩膀上。

### 核心框架
- **[LangChain](https://github.com/langchain-ai/langchain)**：支持我们 LLM 交互和链的卓越框架。
- **[LangGraph](https://github.com/langchain-ai/langgraph)**：实现复杂的多智能体编排。
- **[Next.js](https://nextjs.org/)**：构建 Web 应用程序的前沿框架。

### UI 库
- **[Shadcn](https://ui.shadcn.com/)**：支持我们 UI 的简约组件。
- **[Zustand](https://zustand.docs.pmnd.rs/)**：令人惊叹的状态管理库。
- **[Framer Motion](https://www.framer.com/motion/)**：出色的动画库。
- **[React Markdown](https://www.npmjs.com/package/react-markdown)**：具有可定制性的卓越 markdown 渲染。
- **[SToneX](https://github.com/stonexer)**：感谢他对逐字符视觉效果的宝贵贡献。

这些杰出的项目构成了 DeerFlow 的骨干，体现了开源协作的变革力量。

### 特别感谢
最后，我们要向 `DeerFlow` 的核心作者表达衷心的感谢：

- **[Daniel Walnut](https://github.com/hetaoBackend/)**
- **[Henry Li](https://github.com/magiccube/)**

没有他们的愿景、热情和奉献，`DeerFlow` 就不会有今天的成就。

================
File: web/src/app/settings/tabs/index.tsx
================
import { Settings, type LucideIcon } from "lucide-react";
import { AboutTab } from "./about-tab";
import { GeneralTab } from "./general-tab";
import { MCPTab } from "./mcp-tab";

================
File: web/src/app/page.tsx
================
import { useTranslations } from 'next-intl';
import { useMemo } from "react";
import { SiteHeader } from "./chat/components/site-header";
import { Jumbotron } from "./landing/components/jumbotron";
import { Ray } from "./landing/components/ray";
import { CaseStudySection } from "./landing/sections/case-study-section";
import { CoreFeatureSection } from "./landing/sections/core-features-section";
import { JoinCommunitySection } from "./landing/sections/join-community-section";
import { MultiAgentSection } from "./landing/sections/multi-agent-section";

================
File: web/src/components/deer-flow/icons/enhance.tsx
================
import type { SVGProps } from "react";
export function Enhance(props: SVGProps<SVGSVGElement>)

================
File: web/src/components/deer-flow/icons/report-style.tsx
================
export function ReportStyle(

================
File: web/src/components/deer-flow/language-switcher.tsx
================
import { useLocale } from "next-intl";
import { useRouter } from "next/navigation";
import { useTransition } from "react";
import { Button } from "~/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
type LanguageOption = {
  code: string;
  name: string;
  flag: string;
};
⋮----
const handleLanguageChange = (newLocale: string) =>

================
File: web/src/components/deer-flow/markdown.tsx
================
import { Check, Copy } from "lucide-react";
import { useMemo, useState } from "react";
import ReactMarkdown, {
  type Options as ReactMarkdownOptions,
} from "react-markdown";
import rehypeKatex from "rehype-katex";
import remarkGfm from "remark-gfm";
import remarkMath from "remark-math";
⋮----
import { Button } from "~/components/ui/button";
import { rehypeSplitWordsIntoSpans } from "~/core/rehype";
import { autoFixMarkdown } from "~/core/utils/markdown";
import { cn } from "~/lib/utils";
import Image from "./image";
import { Tooltip } from "./tooltip";
import { Link } from "./link";

================
File: web/src/components/editor/index.tsx
================
import {
  EditorCommand,
  EditorCommandEmpty,
  EditorCommandItem,
  EditorCommandList,
  EditorContent,
  type EditorInstance,
  EditorRoot,
  ImageResizer,
  type JSONContent,
  handleCommandNavigation,
  handleImageDrop,
  handleImagePaste,
} from "novel";
import type { Content } from "@tiptap/react";
import { useEffect, useState } from "react";
import { useDebouncedCallback } from "use-debounce";
import { defaultExtensions } from "./extensions";
import { ColorSelector } from "./selectors/color-selector";
import { LinkSelector } from "./selectors/link-selector";
import { MathSelector } from "./selectors/math-selector";
import { NodeSelector } from "./selectors/node-selector";
import { Separator } from "../ui/separator";
import GenerativeMenuSwitch from "./generative/generative-menu-switch";
import { uploadFn } from "./image-upload";
import { TextButtons } from "./selectors/text-buttons";
import { slashCommand, suggestionItems } from "./slash-command";
⋮----
export interface ReportEditorProps {
  content: Content;
  onMarkdownChange?: (markdown: string) => void;
}
⋮----
const highlightCodeblocks = (content: string) =>

================
File: web/src/components/magicui/border-beam.tsx
================
import { cn } from "~/lib/utils";
import { motion, type MotionStyle, type Transition } from "motion/react";
interface BorderBeamProps {
  size?: number;
  duration?: number;
  delay?: number;
  colorFrom?: string;
  colorTo?: string;
  transition?: Transition;
  className?: string;
  style?: React.CSSProperties;
  reverse?: boolean;
  initialOffset?: number;
}

================
File: web/src/core/api/index.ts
================


================
File: web/src/core/api/mcp.ts
================
import type { SimpleMCPServerMetadata } from "../mcp";
import { resolveServiceURL } from "./resolve-service-url";
export async function queryMCPServerMetadata(config: SimpleMCPServerMetadata, signal?: AbortSignal)

================
File: web/src/core/api/prompt-enhancer.ts
================
import { resolveServiceURL } from "./resolve-service-url";
export interface EnhancePromptRequest {
  prompt: string;
  context?: string;
  report_style?: string;
}
export interface EnhancePromptResponse {
  enhanced_prompt: string;
}
export async function enhancePrompt(
  request: EnhancePromptRequest,
): Promise<string>

================
File: web/src/core/api/types.ts
================
import type { Option } from "../messages";
export interface ToolCall {
  type: "tool_call";
  id: string;
  name: string;
  args: Record<string, unknown>;
}
export interface ToolCallChunk {
  type: "tool_call_chunk";
  index: number;
  id: string;
  name: string;
  args: string;
}
interface GenericEvent<T extends string, D extends object> {
  type: T;
  data: {
    id: string;
    thread_id: string;
    agent: "coordinator" | "planner" | "researcher" | "coder" | "reporter";
    role: "user" | "assistant" | "tool";
    finish_reason?: "stop" | "tool_calls" | "interrupt";
  } & D;
}
export interface MessageChunkEvent
  extends GenericEvent<
    "message_chunk",
    {
      content?: string;
      reasoning_content?: string;
    }
  > {}
export interface ToolCallsEvent
  extends GenericEvent<
    "tool_calls",
    {
      tool_calls: ToolCall[];
      tool_call_chunks: ToolCallChunk[];
    }
  > {}
export interface ToolCallChunksEvent
  extends GenericEvent<
    "tool_call_chunks",
    {
      tool_call_chunks: ToolCallChunk[];
    }
  > {}
export interface ToolCallResultEvent
  extends GenericEvent<
    "tool_call_result",
    {
      tool_call_id: string;
      content?: string;
    }
  > {}
export interface InterruptEvent
  extends GenericEvent<
    "interrupt",
    {
      options: Option[];
    }
  > {}
export type ChatEvent =
  | MessageChunkEvent
  | ToolCallsEvent
  | ToolCallChunksEvent
  | ToolCallResultEvent
  | InterruptEvent;

================
File: web/src/core/config/index.ts
================


================
File: web/src/core/config/types.ts
================
export interface ModelConfig {
  basic: string[];
  reasoning: string[];
}
export interface RagConfig {
  provider: string;
}
export interface DeerFlowConfig {
  rag: RagConfig;
  models: ModelConfig;
}

================
File: web/src/core/messages/merge-message.ts
================
import type {
  ChatEvent,
  InterruptEvent,
  MessageChunkEvent,
  ToolCallChunksEvent,
  ToolCallResultEvent,
  ToolCallsEvent,
} from "../api";
import { deepClone } from "../utils/deep-clone";
import type { Message } from "./types";
export function mergeMessage(message: Message, event: ChatEvent)
function mergeTextMessage(message: Message, event: MessageChunkEvent)
function mergeToolCallMessage(
  message: Message,
  event: ToolCallsEvent | ToolCallChunksEvent,
)
function mergeToolCallResultMessage(
  message: Message,
  event: ToolCallResultEvent,
)
function mergeInterruptMessage(message: Message, event: InterruptEvent)

================
File: web/src/styles/globals.css
================
@plugin "@tailwindcss/typography";
⋮----
@theme {
@theme inline {
:root {
.dark {
@layer base {
⋮----
* {
body {
⋮----
input,
[role="button"],

================
File: web/src/i18n.ts
================
import { cookies } from "next/headers";
import { getRequestConfig } from "next-intl/server";

================
File: web/next.config.js
================
const withNextIntl = createNextIntlPlugin('./src/i18n.ts');
⋮----
webpack: (config) => {
config.module.rules.push({
⋮----
export default withNextIntl(config);

================
File: .dockerignore
================
.env
Dockerfile
.dockerignore
.git
.gitignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
.venv/

# Web
node_modules
npm-debug.log
.next

# IDE
.idea/
.vscode/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Project specific
conf.yaml
web/
docs/
examples/
assets/
tests/
*.log

================
File: web/messages/zh.json
================
{
  "common": {
    "cancel": "取消",
    "save": "保存",
    "settings": "设置",
    "getStarted": "开始使用",
    "learnMore": "了解更多",
    "starOnGitHub": "在 GitHub 上点赞",
    "send": "发送",
    "stop": "停止",
    "linkNotReliable": "此链接可能是 AI 生成的幻觉，可能并不可靠。",
    "noResult": "无结果"
  },
  "messageInput": {
    "placeholder": "我能帮你做什么？",
    "placeholderWithRag": "我能帮你做什么？\n你可以通过 @ 引用 RAG 资源。"
  },
  "header": {
    "title": "DeerFlow"
  },
  "hero": {
    "title": "深度研究",
    "subtitle": "触手可及",
    "description": "认识 DeerFlow，您的个人深度研究助手。凭借搜索引擎、网络爬虫、Python 和 MCP 服务等强大工具，它能提供即时洞察、全面报告，甚至制作引人入胜的播客。",
    "footnote": "* DEER 代表深度探索和高效研究。"
  },
  "settings": {
    "title": "DeerFlow 设置",
    "description": "在这里管理您的 DeerFlow 设置。",
    "cancel": "取消",
    "addServers": "添加服务器",
    "addNewMCPServers": "添加新的 MCP 服务器",
    "mcpConfigDescription": "DeerFlow 使用标准 JSON MCP 配置来创建新服务器。",
    "pasteConfigBelow": "将您的配置粘贴到下面，然后点击\"添加\"来添加新服务器。",
    "add": "添加",
    "general": {
      "title": "通用",
      "autoAcceptPlan": "允许自动接受计划",
      "maxPlanIterations": "最大计划迭代次数",
      "maxPlanIterationsDescription": "设置为 1 进行单步规划。设置为 2 或更多以启用重新规划。",
      "maxStepsOfPlan": "研究计划的最大步骤数",
      "maxStepsDescription": "默认情况下，每个研究计划有 3 个步骤。",
      "maxSearchResults": "最大搜索结果数",
      "maxSearchResultsDescription": "默认情况下，每个搜索步骤有 3 个结果。"
    },
    "mcp": {
      "title": "MCP 服务器",
      "description": "模型上下文协议通过集成外部工具来增强 DeerFlow，用于私域搜索、网页浏览、订餐等任务。点击这里",
      "learnMore": "了解更多关于 MCP 的信息。",
      "enableDisable": "启用/禁用服务器",
      "deleteServer": "删除服务器",
      "disabled": "已禁用",
      "new": "新增"
    },
    "about": {
      "title": "关于"
    },
    "reportStyle": {
      "writingStyle": "写作风格",
      "chooseTitle": "选择写作风格",
      "chooseDesc": "请选择您的研究报告的写作风格。不同风格适用于不同受众和用途。",
      "academic": "学术",
      "academicDesc": "正式、客观、分析性强，术语精确",
      "popularScience": "科普",
      "popularScienceDesc": "生动有趣，适合大众阅读",
      "news": "新闻",
      "newsDesc": "事实、简明、公正的新闻风格",
      "socialMedia": "社交媒体",
      "socialMediaDesc": "简洁有趣，易于传播"
    }
  },
  "footer": {
    "quote": "源于开源，回馈开源。",
    "license": "基于 MIT 许可证授权",
    "copyright": "DeerFlow"
  },
  "chat": {
    "page": {
      "loading": "正在加载 DeerFlow...",
      "welcomeUser": "欢迎，{username}",
      "starOnGitHub": "在 GitHub 上点赞"
    },
    "welcome": {
      "greeting": "👋 你好！",
      "description": "欢迎来到 🦌 DeerFlow，一个基于前沿语言模型构建的深度研究助手，帮助您搜索网络、浏览信息并处理复杂任务。"
    },
    "conversationStarters": [
      "埃菲尔铁塔比世界最高建筑高多少倍？",
      "特斯拉电池的平均寿命比汽油发动机长多少年？",
      "生产1公斤牛肉需要多少升水？",
      "光速比声速快多少倍？"
    ],
    "inputBox": {
      "deepThinking": "深度思考",
      "deepThinkingTooltip": {
        "title": "深度思考模式：{status}",
        "description": "启用后，DeerFlow 将使用推理模型（{model}）生成更深思熟虑的计划。"
      },
      "investigation": "调研",
      "investigationTooltip": {
        "title": "调研模式：{status}",
        "description": "启用后，DeerFlow 将在规划前进行快速搜索。这对于与时事和新闻相关的研究很有用。"
      },
      "enhancePrompt": "用 AI 增强提示",
      "on": "开启",
      "off": "关闭"
    },
    "research": {
      "deepResearch": "深度研究",
      "researching": "研究中...",
      "generatingReport": "生成报告中...",
      "reportGenerated": "报告已生成",
      "open": "打开",
      "close": "关闭",
      "deepThinking": "深度思考",
      "report": "报告",
      "activities": "活动",
      "generatePodcast": "生成播客",
      "edit": "编辑",
      "copy": "复制",
      "downloadReport": "下载报告为 Markdown",
      "searchingFor": "搜索",
      "reading": "阅读中",
      "runningPythonCode": "运行 Python 代码",
      "errorExecutingCode": "执行上述代码时出错",
      "executionOutput": "执行输出",
      "retrievingDocuments": "从 RAG 检索文档",
      "running": "运行",
      "generatingPodcast": "生成播客中...",
      "nowPlayingPodcast": "正在播放播客...",
      "podcast": "播客",
      "errorGeneratingPodcast": "生成播客时出错。请重试。",
      "downloadPodcast": "下载播客"
    },
    "messages": {
      "replaying": "回放中",
      "replayDescription": "DeerFlow 正在回放对话...",
      "replayHasStopped": "回放已停止。",
      "replayModeDescription": "您现在处于 DeerFlow 的回放模式。点击右侧的\"播放\"按钮开始。",
      "play": "播放",
      "fastForward": "快进",
      "demoNotice": "* 此网站仅用于演示目的。如果您想尝试自己的问题，请",
      "clickHere": "点击这里",
      "cloneLocally": "在本地克隆并运行它。"
    },
    "multiAgent": {
      "moveToPrevious": "移动到上一步",
      "playPause": "播放 / 暂停",
      "moveToNext": "移动到下一步",
      "toggleFullscreen": "切换全屏"
    }
  },
  "landing": {
    "caseStudies": {
      "title": "案例研究",
      "description": "通过回放查看 DeerFlow 的实际应用。",
      "clickToWatch": "点击观看回放",
      "cases": [
        {
          "title": "埃菲尔铁塔与最高建筑相比有多高？",
          "description": "该研究比较了埃菲尔铁塔和哈利法塔的高度和全球意义，并使用 Python 代码计算倍数。"
        },
        {
          "title": "GitHub 上最热门的仓库有哪些？",
          "description": "该研究利用 MCP 服务识别最受欢迎的 GitHub 仓库，并使用搜索引擎详细记录它们。"
        },
        {
          "title": "写一篇关于南京传统菜肴的文章",
          "description": "该研究通过丰富的内容和图像生动地展示了南京的著名菜肴，揭示了它们隐藏的历史和文化意义。"
        },
        {
          "title": "如何装饰小型出租公寓？",
          "description": "该研究为读者提供了实用而直接的公寓装饰方法，并配有鼓舞人心的图像。"
        },
        {
          "title": "介绍电影《这个杀手不太冷》",
          "description": "该研究全面介绍了电影《这个杀手不太冷》，包括其情节、角色和主题。"
        },
        {
          "title": "你如何看待中国的外卖大战？（中文）",
          "description": "该研究分析了京东和美团之间日益激烈的竞争，突出了它们的策略、技术创新和挑战。"
        },
        {
          "title": "超加工食品与健康有关吗？",
          "description": "该研究检查了超加工食品消费增加的健康风险，敦促对长期影响和个体差异进行更多研究。"
        },
        {
          "title": "写一篇关于\"你会为你的 AI 双胞胎投保吗？\"的文章",
          "description": "该研究探讨了为 AI 双胞胎投保的概念，突出了它们的好处、风险、伦理考虑和不断发展的监管。"
        }
      ]
    },
    "coreFeatures": {
      "title": "核心功能",
      "description": "了解是什么让 DeerFlow 如此有效。",
      "features": [
        {
          "name": "深入挖掘，触及更广",
          "description": "使用高级工具解锁更深层的洞察。我们强大的搜索+爬取和 Python 工具收集全面的数据，提供深入的报告来增强您的研究。"
        },
        {
          "name": "人机协作",
          "description": "通过简单的自然语言完善您的研究计划或调整重点领域。"
        },
        {
          "name": "Lang 技术栈",
          "description": "使用 LangChain 和 LangGraph 框架自信地构建。"
        },
        {
          "name": "MCP 集成",
          "description": "通过无缝的 MCP 集成增强您的研究工作流程并扩展您的工具包。"
        },
        {
          "name": "播客生成",
          "description": "从报告中即时生成播客。非常适合移动学习或轻松分享发现。"
        }
      ]
    },
    "multiAgent": {
      "title": "多智能体架构",
      "description": "通过我们的监督者 + 交接设计模式体验智能体团队合作。"
    },
    "joinCommunity": {
      "title": "加入 DeerFlow 社区",
      "description": "贡献精彩想法，塑造 DeerFlow 的未来。协作、创新并产生影响。",
      "contributeNow": "立即贡献"
    }
  }
}

================
File: web/src/app/chat/components/research-block.tsx
================
import { Check, Copy, Headphones, Pencil, Undo2, X, Download } from "lucide-react";
import { useTranslations } from "next-intl";
import { useCallback, useEffect, useState } from "react";
import { ScrollContainer } from "~/components/deer-flow/scroll-container";
import { Tooltip } from "~/components/deer-flow/tooltip";
import { Button } from "~/components/ui/button";
import { Card } from "~/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { useReplay } from "~/core/replay";
import { closeResearch, listenToPodcast, useStore } from "~/core/store";
import { cn } from "~/lib/utils";
import { ResearchActivitiesBlock } from "./research-activities-block";
import { ResearchReportBlock } from "./research-report-block";
⋮----
const pad = (n: number)
⋮----
<Tooltip title=

================
File: web/src/app/settings/tabs/about-tab.tsx
================
import { BadgeInfo } from "lucide-react";
import { useLocale, useTranslations } from "next-intl";
import { Markdown } from "~/components/deer-flow/markdown";
import aboutEn from "./about-en.md";
import aboutZh from "./about-zh.md";
import type { Tab } from "./types";
export const AboutTab: Tab = () =>

================
File: web/src/components/deer-flow/report-style-dialog.tsx
================
import { useTranslations } from "next-intl";
import { useState } from "react";
import { Check, FileText, Newspaper, Users, GraduationCap } from "lucide-react";
import { Button } from "~/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "~/components/ui/dialog";
import { setReportStyle, useSettingsStore } from "~/core/store";
import { cn } from "~/lib/utils";
import { Tooltip } from "./tooltip";
⋮----
const handleStyleChange = (
    style: "academic" | "popular_science" | "news" | "social_media",
) =>
⋮----

⋮----
className=

================
File: web/src/components/deer-flow/resource-suggestion.tsx
================
import type { MentionOptions } from "@tiptap/extension-mention";
import { ReactRenderer } from "@tiptap/react";
import {
  ResourceMentions,
  type ResourceMentionsProps,
} from "./resource-mentions";
import type { Instance, Props } from "tippy.js";
import tippy from "tippy.js";
import { resolveServiceURL } from "~/core/api/resolve-service-url";
import type { Resource } from "~/core/messages";
⋮----
onUpdate(props)
onKeyDown(props)
onExit()

================
File: web/src/components/deer-flow/scroll-container.tsx
================
import {
  useEffect,
  useImperativeHandle,
  useLayoutEffect,
  useRef,
  type ReactNode,
  type RefObject,
} from "react";
import { useStickToBottom } from "use-stick-to-bottom";
import { ScrollArea } from "~/components/ui/scroll-area";
import { cn } from "~/lib/utils";
export interface ScrollContainerProps {
  className?: string;
  children?: ReactNode;
  scrollShadow?: boolean;
  scrollShadowColor?: string;
  autoScrollToBottom?: boolean;
  ref?: RefObject<ScrollContainerRef | null>;
}
export interface ScrollContainerRef {
  scrollToBottom(): void;
}
⋮----
scrollToBottom(): void;
⋮----
scrollToBottom()
⋮----
className=

================
File: web/src/core/api/rag.ts
================
import type { Resource } from "../messages";
import { resolveServiceURL } from "./resolve-service-url";
export function queryRAGResources(query: string)

================
File: web/src/core/messages/types.ts
================
export type MessageRole = "user" | "assistant" | "tool";
export interface Message {
  id: string;
  threadId: string;
  agent?:
    | "coordinator"
    | "planner"
    | "researcher"
    | "coder"
    | "reporter"
    | "podcast";
  role: MessageRole;
  isStreaming?: boolean;
  content: string;
  contentChunks: string[];
  reasoningContent?: string;
  reasoningContentChunks?: string[];
  toolCalls?: ToolCallRuntime[];
  options?: Option[];
  finishReason?: "stop" | "interrupt" | "tool_calls";
  interruptFeedback?: string;
  resources?: Array<Resource>;
}
export interface Option {
  text: string;
  value: string;
}
export interface ToolCallRuntime {
  id: string;
  name: string;
  args: Record<string, unknown>;
  argsChunks?: string[];
  result?: string;
}
export interface Resource {
  uri: string;
  title: string;
}

================
File: web/src/core/utils/json.ts
================
import { parse } from "best-effort-json-parser";
export function parseJSON<T>(json: string | null | undefined, fallback: T)

================
File: web/src/styles/prosemirror.css
================
.prose {
.prose.inline-editor * {
.prose.inline-editor .is-empty {
.prose.inline-editor .is-empty.placeholder {
.ProseMirror {
.ProseMirror .is-editor-empty:first-child::before {
.ProseMirror p.is-empty::before {
.ProseMirror .mention {
.ProseMirror img {
⋮----
&:hover {
&.ProseMirror-selectednode {
⋮----
.img-placeholder {
⋮----
&:before {
⋮----
.ProseMirror pre {
⋮----
code {
.hljs-comment,
.hljs-variable,
.hljs-number,
.hljs-string,
.hljs-title,
.hljs-keyword,
.hljs-emphasis {
.hljs-strong {
⋮----
ul[data-type="taskList"] li > label {
⋮----
ul[data-type="taskList"] li > label input[type="checkbox"] {
⋮----
&:active {
&::before {
&:checked::before {
⋮----
ul[data-type="taskList"] li[data-checked="true"] > div > p {
.tippy-box {
.ProseMirror:not(.dragging) .ProseMirror-selectednode {
.drag-handle {
⋮----
&.hide {
⋮----
.dark .drag-handle {
iframe {
div[data-youtube-video] > iframe {
.ProseMirror-selectednode iframe {
⋮----
span[style] > strong {
mark[style] > strong {

================
File: web/src/app/chat/components/messages-block.tsx
================
import { motion } from "framer-motion";
import { FastForward, Play } from "lucide-react";
import { useTranslations } from "next-intl";
import { useCallback, useRef, useState } from "react";
import { RainbowText } from "~/components/deer-flow/rainbow-text";
import { Button } from "~/components/ui/button";
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import { fastForwardReplay } from "~/core/api";
import { useReplayMetadata } from "~/core/api/hooks";
import type { Option, Resource } from "~/core/messages";
import { useReplay } from "~/core/replay";
import { sendMessage, useMessageIds, useStore } from "~/core/store";
import { env } from "~/env";
import { cn } from "~/lib/utils";
import { ConversationStarter } from "./conversation-starter";
import { InputBox } from "./input-box";
import { MessageListView } from "./message-list-view";
import { Welcome } from "./welcome";
⋮----
className=

================
File: web/src/app/chat/components/research-activities-block.tsx
================
import { PythonOutlined } from "@ant-design/icons";
import { motion } from "framer-motion";
import { LRUCache } from "lru-cache";
import { BookOpenText, FileText, PencilRuler, Search } from "lucide-react";
import { useTranslations } from "next-intl";
import { useTheme } from "next-themes";
import { useMemo } from "react";
import SyntaxHighlighter from "react-syntax-highlighter";
import { docco } from "react-syntax-highlighter/dist/esm/styles/hljs";
import { dark } from "react-syntax-highlighter/dist/esm/styles/prism";
import { FavIcon } from "~/components/deer-flow/fav-icon";
import Image from "~/components/deer-flow/image";
import { LoadingAnimation } from "~/components/deer-flow/loading-animation";
import { Markdown } from "~/components/deer-flow/markdown";
import { RainbowText } from "~/components/deer-flow/rainbow-text";
import { Tooltip } from "~/components/deer-flow/tooltip";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "~/components/ui/accordion";
import { Skeleton } from "~/components/ui/skeleton";
import { findMCPTool } from "~/core/mcp";
import type { ToolCallRuntime } from "~/core/messages";
import { useMessage, useStore } from "~/core/store";
import { parseJSON } from "~/core/utils";
import { cn } from "~/lib/utils";
⋮----
<ul className=

================
File: web/src/app/settings/tabs/mcp-tab.tsx
================
import { motion } from "framer-motion";
import { Blocks, PencilRuler, Trash } from "lucide-react";
import { useTranslations } from "next-intl";
import { useCallback, useState } from "react";
import { Tooltip } from "~/components/deer-flow/tooltip";
import { Button } from "~/components/ui/button";
import { Switch } from "~/components/ui/switch";
import type { MCPServerMetadata } from "~/core/mcp";
import { cn } from "~/lib/utils";
import { AddMCPServerDialog } from "../dialogs/add-mcp-server-dialog";
import type { Tab } from "./types";
⋮----
<Tooltip title=
⋮----
handleToggleServer(server.name, checked);

================
File: web/src/app/layout.tsx
================
import { type Metadata } from "next";
import { Geist } from "next/font/google";
import Script from "next/script";
import { NextIntlClientProvider } from 'next-intl';
import { getLocale, getMessages } from 'next-intl/server';
import { ThemeProviderWrapper } from "~/components/deer-flow/theme-provider-wrapper";
import { env } from "~/env";
import { Toaster } from "../components/deer-flow/toaster";

================
File: web/src/components/deer-flow/resource-mentions.tsx
================
import { useTranslations } from "next-intl";
import { forwardRef, useEffect, useImperativeHandle, useState } from "react";
import type { Resource } from "~/core/messages";
import { cn } from "~/lib/utils";
export interface ResourceMentionsProps {
  items: Array<Resource>;
  command: (item: { id: string; label: string }) => void;
}
⋮----
const selectItem = (index: number) =>
const upHandler = () =>
const downHandler = () =>
const enterHandler = () =>

================
File: web/src/core/api/hooks.ts
================
import { useEffect, useRef, useState } from "react";
import { env } from "~/env";
import type { DeerFlowConfig } from "../config";
import { useReplay } from "../replay";
import { fetchReplayTitle } from "./chat";
import { resolveServiceURL } from "./resolve-service-url";
export function useReplayMetadata()
export function useConfig():

================
File: web/src/core/store/settings-store.ts
================
import { create } from "zustand";
import type { MCPServerMetadata, SimpleMCPServerMetadata } from "../mcp";
⋮----
export type SettingsState = {
  general: {
    autoAcceptedPlan: boolean;
    enableDeepThinking: boolean;
    enableBackgroundInvestigation: boolean;
    maxPlanIterations: number;
    maxStepNum: number;
    maxSearchResults: number;
    reportStyle: "academic" | "popular_science" | "news" | "social_media";
  };
  mcp: {
    servers: MCPServerMetadata[];
  };
};
⋮----
export const useSettings = (key: keyof SettingsState) =>
export const changeSettings = (settings: SettingsState) =>
export const loadSettings = () =>
export const saveSettings = () =>
export const getChatStreamSettings = () =>
export function setReportStyle(
  value: "academic" | "popular_science" | "news" | "social_media",
)
export function setEnableDeepThinking(value: boolean)
export function setEnableBackgroundInvestigation(value: boolean)

================
File: web/package.json
================
{
  "name": "deer-flow-web",
  "version": "0.1.0",
  "private": true,
  "type": "module",
  "scripts": {
    "build": "next build",
    "check": "next lint && tsc --noEmit",
    "dev": "dotenv -e ../.env -- next dev --turbo",
    "scan": "next dev & npx react-scan@latest localhost:3000",
    "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,mdx}\" --cache",
    "format:write": "prettier --write \"**/*.{ts,tsx,js,jsx,mdx}\" --cache",
    "lint": "next lint",
    "lint:fix": "next lint --fix",
    "preview": "next build && next start",
    "start": "next start",
    "typecheck": "tsc --noEmit"
  },
  "dependencies": {
    "@ant-design/icons": "^6.0.0",
    "@hookform/resolvers": "^5.0.1",
    "@radix-ui/react-accordion": "^1.2.8",
    "@radix-ui/react-checkbox": "^1.2.3",
    "@radix-ui/react-collapsible": "^1.1.8",
    "@radix-ui/react-dialog": "^1.1.10",
    "@radix-ui/react-dropdown-menu": "^2.1.11",
    "@radix-ui/react-icons": "^1.3.2",
    "@radix-ui/react-label": "^2.1.4",
    "@radix-ui/react-popover": "^1.1.11",
    "@radix-ui/react-scroll-area": "^1.2.4",
    "@radix-ui/react-select": "^2.2.2",
    "@radix-ui/react-separator": "^1.1.4",
    "@radix-ui/react-slider": "^1.3.2",
    "@radix-ui/react-slot": "^1.2.0",
    "@radix-ui/react-switch": "^1.2.2",
    "@radix-ui/react-tabs": "^1.1.4",
    "@radix-ui/react-tooltip": "^1.2.0",
    "@rc-component/mentions": "^1.2.0",
    "@t3-oss/env-nextjs": "^0.11.0",
    "@tailwindcss/typography": "^0.5.16",
    "@tiptap/extension-document": "^2.12.0",
    "@tiptap/extension-mention": "^2.12.0",
    "@tiptap/extension-table": "^2.11.7",
    "@tiptap/extension-table-cell": "^2.11.7",
    "@tiptap/extension-table-header": "^2.11.7",
    "@tiptap/extension-table-row": "^2.11.7",
    "@tiptap/extension-text": "^2.12.0",
    "@tiptap/react": "^2.11.7",
    "@types/js-cookie": "^3.0.6",
    "@xyflow/react": "^12.6.0",
    "best-effort-json-parser": "^1.1.3",
    "class-variance-authority": "^0.7.1",
    "clsx": "^2.1.1",
    "cmdk": "^1.1.1",
    "framer-motion": "^12.6.5",
    "hast": "^1.0.0",
    "highlight.js": "^11.11.1",
    "immer": "^10.1.1",
    "js-cookie": "^3.0.5",
    "katex": "^0.16.21",
    "lowlight": "^3.3.0",
    "lru-cache": "^11.1.0",
    "lucide-react": "^0.487.0",
    "motion": "^12.7.4",
    "nanoid": "^5.1.5",
    "next": "^15.2.3",
    "next-intl": "^4.3.1",
    "next-themes": "^0.4.6",
    "novel": "^1.0.2",
    "react": "^19.0.0",
    "react-dom": "^19.0.0",
    "react-hook-form": "^7.56.1",
    "react-markdown": "^10.1.0",
    "react-syntax-highlighter": "^15.6.1",
    "rehype-katex": "^7.0.1",
    "remark-gfm": "^4.0.1",
    "remark-math": "^6.0.0",
    "sonner": "^2.0.3",
    "tailwind-merge": "^3.2.0",
    "tippy.js": "^6.3.7",
    "tiptap-markdown": "^0.8.10",
    "tw-animate-css": "^1.2.5",
    "unist-util-visit": "^5.0.0",
    "use-debounce": "^10.0.4",
    "use-stick-to-bottom": "^1.1.0",
    "zod": "^3.24.3",
    "zustand": "^5.0.3"
  },
  "devDependencies": {
    "@eslint/eslintrc": "^3.3.1",
    "@tailwindcss/postcss": "^4.0.15",
    "@types/hast": "^3.0.4",
    "@types/node": "^20.14.10",
    "@types/react": "^19.0.0",
    "@types/react-dom": "^19.0.0",
    "@types/react-syntax-highlighter": "^15.5.13",
    "dotenv-cli": "^8.0.0",
    "eslint": "^9.23.0",
    "eslint-config-next": "^15.2.3",
    "postcss": "^8.5.3",
    "prettier": "^3.5.3",
    "prettier-plugin-tailwindcss": "^0.6.11",
    "raw-loader": "^4.0.2",
    "tailwindcss": "^4.0.15",
    "typescript": "^5.8.2",
    "typescript-eslint": "^8.27.0"
  },
  "ct3aMetadata": {
    "initVersion": "7.39.3"
  },
  "packageManager": "pnpm@10.6.5",
  "pnpm": {
    "ignoredBuiltDependencies": [
      "sharp"
    ]
  }
}

================
File: web/messages/en.json
================
{
  "common": {
    "cancel": "Cancel",
    "save": "Save",
    "settings": "Settings",
    "getStarted": "Get Started",
    "learnMore": "Learn More",
    "starOnGitHub": "Star on GitHub",
    "send": "Send",
    "stop": "Stop",
    "linkNotReliable": "This link might be a hallucination from AI model and may not be reliable.",
    "noResult": "No result"
  },
  "messageInput": {
    "placeholder": "What can I do for you?",
    "placeholderWithRag": "What can I do for you? \nYou may refer to RAG resources by using @."
  },
  "header": {
    "title": "DeerFlow"
  },
  "hero": {
    "title": "Deep Research",
    "subtitle": "at Your Fingertips",
    "description": "Meet DeerFlow, your personal Deep Research assistant. With powerful tools like search engines, web crawlers, Python and MCP services, it delivers instant insights, comprehensive reports, and even captivating podcasts.",
    "footnote": "* DEER stands for Deep Exploration and Efficient Research."
  },
  "settings": {
    "title": "DeerFlow Settings",
    "description": "Manage your DeerFlow settings here.",
    "addServers": "Add Servers",
    "cancel": "Cancel",
    "addNewMCPServers": "Add New MCP Servers",
    "mcpConfigDescription": "DeerFlow uses the standard JSON MCP config to create a new server.",
    "pasteConfigBelow": "Paste your config below and click \"Add\" to add new servers.",
    "add": "Add",
    "general": {
      "title": "General",
      "autoAcceptPlan": "Allow automatic acceptance of plans",
      "maxPlanIterations": "Max plan iterations",
      "maxPlanIterationsDescription": "Set to 1 for single-step planning. Set to 2 or more to enable re-planning.",
      "maxStepsOfPlan": "Max steps of a research plan",
      "maxStepsDescription": "By default, each research plan has 3 steps.",
      "maxSearchResults": "Max search results",
      "maxSearchResultsDescription": "By default, each search step has 3 results."
    },
    "mcp": {
      "title": "MCP Servers",
      "description": "The Model Context Protocol boosts DeerFlow by integrating external tools for tasks like private domain searches, web browsing, food ordering, and more. Click here to",
      "learnMore": "learn more about MCP.",
      "enableDisable": "Enable/disable server",
      "deleteServer": "Delete server",
      "disabled": "Disabled",
      "new": "New"
    },
    "about": {
      "title": "About"
    },
    "reportStyle": {
      "writingStyle": "Writing Style",
      "chooseTitle": "Choose Writing Style",
      "chooseDesc": "Select the writing style for your research reports. Different styles are optimized for different audiences and purposes.",
      "academic": "Academic",
      "academicDesc": "Formal, objective, and analytical with precise terminology",
      "popularScience": "Popular Science",
      "popularScienceDesc": "Engaging and accessible for general audience",
      "news": "News",
      "newsDesc": "Factual, concise, and impartial journalistic style",
      "socialMedia": "Social Media",
      "socialMediaDesc": "Concise, attention-grabbing, and shareable"
    }
  },
  "footer": {
    "quote": "Originated from Open Source, give back to Open Source.",
    "license": "Licensed under MIT License",
    "copyright": "DeerFlow"
  },
  "chat": {
    "page": {
        "loading": "Loading DeerFlow...",
        "welcomeUser": "Welcome, {username}",
        "starOnGitHub": "Star DeerFlow on GitHub"
    },
    "welcome": {
      "greeting": "👋 Hello, there!",
      "description": "Welcome to 🦌 DeerFlow, a deep research assistant built on cutting-edge language models, helps you search on web, browse information, and handle complex tasks."
    },
    "conversationStarters": [
      "How many times taller is the Eiffel Tower than the tallest building in the world?",
      "How many years does an average Tesla battery last compared to a gasoline engine?",
      "How many liters of water are required to produce 1 kg of beef?",
      "How many times faster is the speed of light compared to the speed of sound?"
    ],
    "inputBox": {
      "deepThinking": "Deep Thinking",
      "deepThinkingTooltip": {
        "title": "Deep Thinking Mode: {status}",
        "description": "When enabled, DeerFlow will use reasoning model ({model}) to generate more thoughtful plans."
      },
      "investigation": "Investigation",
      "investigationTooltip": {
        "title": "Investigation Mode: {status}",
        "description": "When enabled, DeerFlow will perform a quick search before planning. This is useful for researches related to ongoing events and news."
      },
      "enhancePrompt": "Enhance prompt with AI",
      "on": "On",
      "off": "Off"
    },
    "research": {
      "deepResearch": "Deep Research",
      "researching": "Researching...",
      "generatingReport": "Generating report...",
      "reportGenerated": "Report generated",
      "open": "Open",
      "close": "Close",
      "deepThinking": "Deep Thinking",
      "report": "Report",
      "activities": "Activities",
      "generatePodcast": "Generate podcast",
      "edit": "Edit",
      "copy": "Copy",
      "downloadReport": "Download report as markdown",
      "searchingFor": "Searching for",
      "reading": "Reading",
      "runningPythonCode": "Running Python code",
      "errorExecutingCode": "Error when executing the above code",
      "executionOutput": "Execution output",
      "retrievingDocuments": "Retrieving documents from RAG",
      "running": "Running",
      "generatingPodcast": "Generating podcast...",
      "nowPlayingPodcast": "Now playing podcast...",
      "podcast": "Podcast",
      "errorGeneratingPodcast": "Error when generating podcast. Please try again.",
      "downloadPodcast": "Download podcast"
    },
    "messages": {
      "replaying": "Replaying",
      "replayDescription": "DeerFlow is now replaying the conversation...",
      "replayHasStopped": "The replay has been stopped.",
      "replayModeDescription": "You're now in DeerFlow's replay mode. Click the \"Play\" button on the right to start.",
      "play": "Play",
      "fastForward": "Fast Forward",
      "demoNotice": "* This site is for demo purposes only. If you want to try your own question, please",
      "clickHere": "click here",
      "cloneLocally": "to clone it locally and run it."
    },
    "multiAgent": {
      "moveToPrevious": "Move to the previous step",
      "playPause": "Play / Pause",
      "moveToNext": "Move to the next step",
      "toggleFullscreen": "Toggle fullscreen"
    }
  },
  "landing": {
    "caseStudies": {
      "title": "Case Studies",
      "description": "See DeerFlow in action through replays.",
      "clickToWatch": "Click to watch replay",
      "cases": [
        {
          "title": "How tall is Eiffel Tower compared to tallest building?",
          "description": "The research compares the heights and global significance of the Eiffel Tower and Burj Khalifa, and uses Python code to calculate the multiples."
        },
        {
          "title": "What are the top trending repositories on GitHub?",
          "description": "The research utilized MCP services to identify the most popular GitHub repositories and documented them in detail using search engines."
        },
        {
          "title": "Write an article about Nanjing's traditional dishes",
          "description": "The study vividly showcases Nanjing's famous dishes through rich content and imagery, uncovering their hidden histories and cultural significance."
        },
        {
          "title": "How to decorate a small rental apartment?",
          "description": "The study provides readers with practical and straightforward methods for decorating apartments, accompanied by inspiring images."
        },
        {
          "title": "Introduce the movie 'Léon: The Professional'",
          "description": "The research provides a comprehensive introduction to the movie 'Léon: The Professional', including its plot, characters, and themes."
        },
        {
          "title": "How do you view the takeaway war in China? (in Chinese)",
          "description": "The research analyzes the intensifying competition between JD and Meituan, highlighting their strategies, technological innovations, and challenges."
        },
        {
          "title": "Are ultra-processed foods linked to health?",
          "description": "The research examines the health risks of rising ultra-processed food consumption, urging more research on long-term effects and individual differences."
        },
        {
          "title": "Write an article on \"Would you insure your AI twin?\"",
          "description": "The research explores the concept of insuring AI twins, highlighting their benefits, risks, ethical considerations, and the evolving regulatory."
        }
      ]
    },
    "coreFeatures": {
      "title": "Core Features",
      "description": "Find out what makes DeerFlow effective.",
      "features": [
        {
          "name": "Dive Deeper and Reach Wider",
          "description": "Unlock deeper insights with advanced tools. Our powerful search + crawling and Python tools gathers comprehensive data, delivering in-depth reports to enhance your study."
        },
        {
          "name": "Human-in-the-loop",
          "description": "Refine your research plan, or adjust focus areas all through simple natural language."
        },
        {
          "name": "Lang Stack",
          "description": "Build with confidence using the LangChain and LangGraph frameworks."
        },
        {
          "name": "MCP Integrations",
          "description": "Supercharge your research workflow and expand your toolkit with seamless MCP integrations."
        },
        {
          "name": "Podcast Generation",
          "description": "Instantly generate podcasts from reports. Perfect for on-the-go learning or sharing findings effortlessly."
        }
      ]
    },
    "multiAgent": {
      "title": "Multi-Agent Architecture",
      "description": "Experience the agent teamwork with our Supervisor + Handoffs design pattern."
    },
    "joinCommunity": {
      "title": "Join the DeerFlow Community",
      "description": "Contribute brilliant ideas to shape the future of DeerFlow. Collaborate, innovate, and make impacts.",
      "contributeNow": "Contribute Now"
    }
  }
}

================
File: web/src/app/chat/components/message-list-view.tsx
================
import { LoadingOutlined } from "@ant-design/icons";
import { motion } from "framer-motion";
import {
  Download,
  Headphones,
  ChevronDown,
  ChevronRight,
  Lightbulb,
} from "lucide-react";
import { useTranslations } from "next-intl";
import React, { useCallback, useMemo, useRef, useState } from "react";
import { LoadingAnimation } from "~/components/deer-flow/loading-animation";
import { Markdown } from "~/components/deer-flow/markdown";
import { RainbowText } from "~/components/deer-flow/rainbow-text";
import { RollingText } from "~/components/deer-flow/rolling-text";
import {
  ScrollContainer,
  type ScrollContainerRef,
} from "~/components/deer-flow/scroll-container";
import { Tooltip } from "~/components/deer-flow/tooltip";
import { Button } from "~/components/ui/button";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "~/components/ui/collapsible";
import type { Message, Option } from "~/core/messages";
import {
  closeResearch,
  openResearch,
  useLastFeedbackMessageId,
  useLastInterruptMessage,
  useMessage,
  useMessageIds,
  useResearchMessage,
  useStore,
} from "~/core/store";
import { parseJSON } from "~/core/utils";
import { cn } from "~/lib/utils";
⋮----
className=
⋮----
// 判断是否正在思考：有推理内容但还没有主要内容
⋮----
// 判断是否应该显示计划：有主要内容就显示（无论是否还在流式传输）

================
File: web/src/app/settings/dialogs/add-mcp-server-dialog.tsx
================
import { Loader2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { useCallback, useRef, useState } from "react";
import { Button } from "~/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "~/components/ui/dialog";
import { Textarea } from "~/components/ui/textarea";
import { queryMCPServerMetadata } from "~/core/api";
import {
  MCPConfigSchema,
  type MCPServerMetadata,
  type SimpleMCPServerMetadata,
  type SimpleSSEMCPServerMetadata,
  type SimpleStdioMCPServerMetadata,
} from "~/core/mcp";
⋮----
const handleAbort = () =>

================
File: web/src/core/store/store.ts
================
import { nanoid } from "nanoid";
import { toast } from "sonner";
import { create } from "zustand";
import { useShallow } from "zustand/react/shallow";
import { chatStream, generatePodcast } from "../api";
import type { Message, Resource } from "../messages";
import { mergeMessage } from "../messages";
import { parseJSON } from "../utils";
import { getChatStreamSettings } from "./settings-store";
⋮----
appendMessage(message: Message)
updateMessage(message: Message)
updateMessages(messages: Message[])
openResearch(researchId: string | null)
closeResearch()
setOngoingResearch(researchId: string | null)
⋮----
export async function sendMessage(
  content?: string,
  {
    interruptFeedback,
    resources,
  }: {
    interruptFeedback?: string;
    resources?: Array<Resource>;
  } = {},
  options: { abortSignal?: AbortSignal } = {},
)
⋮----
// Update message status.
// TODO: const isAborted = (error as Error).name === "AbortError";
⋮----
function setResponding(value: boolean)
function existsMessage(id: string)
function getMessage(id: string)
function findMessageByToolCallId(toolCallId: string)
function appendMessage(message: Message)
function updateMessage(message: Message)
function getOngoingResearchId()
function appendResearch(researchId: string)
function appendResearchActivity(message: Message)
export function openResearch(researchId: string | null)
export function closeResearch()
export async function listenToPodcast(researchId: string)
⋮----
// Generating podcast...
⋮----
export function useResearchMessage(researchId: string)
export function useMessage(messageId: string | null | undefined)
export function useMessageIds()
export function useLastInterruptMessage()
export function useLastFeedbackMessageId()
export function useToolCalls()

================
File: web/src/app/settings/tabs/general-tab.tsx
================
import { zodResolver } from "@hookform/resolvers/zod";
import { Settings } from "lucide-react";
import { useTranslations } from "next-intl";
import { useEffect, useMemo } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "~/components/ui/form";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Switch } from "~/components/ui/switch";
import type { SettingsState } from "~/core/store";
import type { Tab } from "./types";

================
File: web/src/components/deer-flow/link.tsx
================
import { useMemo } from "react";
import { useStore, useToolCalls } from "~/core/store";
import { parseJSON } from "~/core/utils/json";
import { Tooltip } from "./tooltip";
import { WarningFilled } from "@ant-design/icons";
import { useTranslations } from "next-intl";
export const Link = ({
  href,
  children,
  checkLinkCredibility = false,
}: {
  href: string | undefined;
  children: React.ReactNode;
  checkLinkCredibility: boolean;
}) =>
⋮----
<Tooltip title=

================
File: web/src/core/api/chat.ts
================
import { env } from "~/env";
import type { MCPServerMetadata } from "../mcp";
import type { Resource } from "../messages";
import { extractReplayIdFromSearchParams } from "../replay/get-replay-id";
import { fetchStream } from "../sse";
import { sleep } from "../utils";
import { resolveServiceURL } from "./resolve-service-url";
import type { ChatEvent } from "./types";
⋮----
export async function fetchReplay(
  url: string,
  options: { abortSignal?: AbortSignal } = {},
)
export async function fetchReplayTitle()
export async function sleepInReplay(ms: number)
⋮----
export function fastForwardReplay(value: boolean)

================
File: web/src/app/chat/components/input-box.tsx
================
import { MagicWandIcon } from "@radix-ui/react-icons";
import { AnimatePresence, motion } from "framer-motion";
import { ArrowUp, Lightbulb, X } from "lucide-react";
import { useTranslations } from "next-intl";
import { useCallback, useMemo, useRef, useState } from "react";
import { Detective } from "~/components/deer-flow/icons/detective";
import MessageInput, {
  type MessageInputRef,
} from "~/components/deer-flow/message-input";
import { ReportStyleDialog } from "~/components/deer-flow/report-style-dialog";
import { Tooltip } from "~/components/deer-flow/tooltip";
import { BorderBeam } from "~/components/magicui/border-beam";
import { Button } from "~/components/ui/button";
import { enhancePrompt } from "~/core/api";
import { useConfig } from "~/core/api/hooks";
import type { Option, Resource } from "~/core/messages";
import {
  setEnableDeepThinking,
  setEnableBackgroundInvestigation,
  useSettingsStore,
} from "~/core/store";
import { cn } from "~/lib/utils";
⋮----
// Clear enhancement animation after sending
⋮----
// Add a small delay for better UX
⋮----
// Update the input with the enhanced prompt with animation
⋮----
// Keep animation for a bit longer to show the effect
⋮----
// Could add toast notification here
⋮----
<Tooltip title=
⋮----
className=

================
File: web/src/components/deer-flow/message-input.tsx
================
import Mention from "@tiptap/extension-mention";
import { Editor, Extension, type Content } from "@tiptap/react";
import {
  EditorContent,
  type EditorInstance,
  EditorRoot,
  type JSONContent,
  StarterKit,
  Placeholder,
} from "novel";
import { Markdown } from "tiptap-markdown";
import { useDebouncedCallback } from "use-debounce";
import { useTranslations } from "next-intl";
⋮----
import { resourceSuggestion } from "./resource-suggestion";
import React, { forwardRef, useEffect, useMemo, useRef } from "react";
import type { Resource } from "~/core/messages";
import { useConfig } from "~/core/api/hooks";
import { LoadingOutlined } from "@ant-design/icons";
import type { DeerFlowConfig } from "~/core/config";
export interface MessageInputRef {
  focus: () => void;
  submit: () => void;
  setContent: (content: string) => void;
}
export interface MessageInputProps {
  className?: string;
  placeholder?: string;
  loading?: boolean;
  config?: DeerFlowConfig | null;
  onChange?: (markdown: string) => void;
  onEnter?: (message: string, resources: Array<Resource>) => void;
}
function formatMessage(content: JSONContent)
function formatItem(item: JSONContent):
⋮----
addKeyboardShortcuts()
⋮----
function transformPastedHTML(html: string)




================================================================
End of Codebase
================================================================
