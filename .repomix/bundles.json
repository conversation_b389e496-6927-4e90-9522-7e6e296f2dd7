{"bundles": {"repo-314": {"name": "repo", "configPath": ".repomix/config/repo-repomix.config.json", "created": "2025-07-25T01:30:26.824Z", "lastUsed": "2025-07-25T01:39:26.662Z", "tags": [], "files": []}, "web-126": {"name": "web", "created": "2025-07-25T01:45:36.783Z", "lastUsed": "2025-07-25T01:57:43.320Z", "tags": [], "files": ["web/next.config.js", "web/docs", "web/messages", "web/src", "web/components.json", "web/package.json", "web/README.md", "web/tsconfig.json", ".dockerignore", "web/.env.example", "web/.gitignore", "web/docker-compose.yml", "web/Dockerfile", "web/eslint.config.js"], "configPath": ".repomix/config/web-repomix.config.json"}}}