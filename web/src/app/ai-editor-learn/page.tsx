// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

"use client";

import { useState } from "react";
import Link from "next/link";
import { ArrowLeft, Sparkles, Code, FileText, Download } from "lucide-react";

import { AIEditor } from "~/components/ai-editor";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "~/components/ui/tabs";
import { Badge } from "~/components/ui/badge";
import { Separator } from "~/components/ui/separator";
import { ScrollArea } from "~/components/ui/scroll-area";
import { ArchitectureGuide } from "./components/architecture-guide";

// 初始内容
const initialContent = {
  type: "doc",
  content: [
    {
      type: "heading",
      attrs: { level: 1 },
      content: [{ type: "text", text: "🤖 AI 编辑器演示" }]
    },
    {
      type: "paragraph",
      content: [
        { type: "text", text: "欢迎使用全新的 AI 编辑器！这是一个从零开始构建的智能写作工具。" }
      ]
    },
    {
      type: "heading",
      attrs: { level: 2 },
      content: [{ type: "text", text: "✨ 主要功能" }]
    },
    {
      type: "bulletList",
      content: [
        {
          type: "listItem",
          content: [
            {
              type: "paragraph",
              content: [
                { type: "text", text: "选中文字后自动显示 AI 工具栏" }
              ]
            }
          ]
        },
        {
          type: "listItem",
          content: [
            {
              type: "paragraph",
              content: [
                { type: "text", text: "快速 AI 提示词：改进、扩展、总结、修正" }
              ]
            }
          ]
        },
        {
          type: "listItem",
          content: [
            {
              type: "paragraph",
              content: [
                { type: "text", text: "自定义 AI 指令" }
              ]
            }
          ]
        },
        {
          type: "listItem",
          content: [
            {
              type: "paragraph",
              content: [
                { type: "text", text: "快捷键支持：Ctrl+K 打开 AI 助手" }
              ]
            }
          ]
        }
      ]
    },
    {
      type: "heading",
      attrs: { level: 2 },
      content: [{ type: "text", text: "🚀 开始体验" }]
    },
    {
      type: "paragraph",
      content: [
        { type: "text", text: "试试选中这段文字，然后点击工具栏中的 AI 按钮，或者按 " },
        { type: "text", text: "Ctrl+K", marks: [{ type: "code" }] },
        { type: "text", text: " 快捷键！" }
      ]
    }
  ]
};

export default function AIEditorDemoPage() {
  const [content, setContent] = useState<any>(initialContent);
  const [markdown, setMarkdown] = useState("");

  const downloadMarkdown = () => {
    const blob = new Blob([markdown], { type: "text/markdown" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "ai-editor-content.md";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-14 items-center">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm" asChild>
              <Link href="/editor-demo">
                <ArrowLeft className="h-4 w-4 mr-2" />
                返回编辑器演示
              </Link>
            </Button>
            <Separator orientation="vertical" className="h-6" />
            <div className="flex items-center space-x-2">
              <Sparkles className="h-5 w-5 text-blue-500" />
              <h1 className="text-lg font-semibold">AI 编辑器演示</h1>
            </div>
          </div>
          <div className="ml-auto flex items-center space-x-2">
            <Badge variant="secondary" className="bg-blue-100 text-blue-800">
              从零构建
            </Badge>
            <Badge variant="outline">
              Novel + TipTap + ProseMirror
            </Badge>
            {markdown && (
              <Button variant="outline" size="sm" onClick={downloadMarkdown}>
                <Download className="h-4 w-4 mr-2" />
                下载 Markdown
              </Button>
            )}
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="container mx-auto p-6">
        <Tabs defaultValue="editor" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="editor">AI 编辑器</TabsTrigger>
            <TabsTrigger value="architecture">架构指南</TabsTrigger>
            <TabsTrigger value="features">功能说明</TabsTrigger>
            <TabsTrigger value="output">Markdown 输出</TabsTrigger>
          </TabsList>
          
          <TabsContent value="editor" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Sparkles className="h-5 w-5 text-blue-500" />
                  <span>AI 智能编辑器</span>
                </CardTitle>
                <CardDescription>
                  选中文字体验 AI 功能，或使用 Ctrl+K 快捷键打开 AI 助手
                </CardDescription>
              </CardHeader>
              <CardContent>
                <AIEditor
                  initialContent={content}
                  onContentChange={setContent}
                  onMarkdownChange={setMarkdown}
                  placeholder="开始写作，选中文字体验 AI 功能..."
                  className="min-h-[500px]"
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="architecture" className="mt-6">
            <ArchitectureGuide />
          </TabsContent>

          <TabsContent value="features" className="mt-6">
            <div className="grid gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>🎯 核心特性</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <h4 className="font-medium">智能工具栏</h4>
                    <p className="text-sm text-muted-foreground">
                      选中文字时自动显示包含 AI 功能的浮动工具栏
                    </p>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-medium">AI 助手面板</h4>
                    <p className="text-sm text-muted-foreground">
                      提供快速提示词和自定义指令功能
                    </p>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-medium">快捷键支持</h4>
                    <p className="text-sm text-muted-foreground">
                      Ctrl+K 打开 AI 助手，Ctrl+Shift+A 快速生成
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>⚡ AI 功能</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <h4 className="font-medium">改进文字</h4>
                    <p className="text-sm text-muted-foreground">
                      让文字更清晰、更有说服力
                    </p>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-medium">扩展内容</h4>
                    <p className="text-sm text-muted-foreground">
                      添加更多细节和例子
                    </p>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-medium">总结要点</h4>
                    <p className="text-sm text-muted-foreground">
                      提取核心要点和关键信息
                    </p>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-medium">修正语法</h4>
                    <p className="text-sm text-muted-foreground">
                      检查并修正语法和表达
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>🏗️ 技术架构</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <h4 className="font-medium">Novel (上层)</h4>
                    <p className="text-sm text-muted-foreground">
                      React 组件封装和 UI 交互
                    </p>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-medium">TipTap (中间层)</h4>
                    <p className="text-sm text-muted-foreground">
                      自定义扩展和命令系统
                    </p>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-medium">ProseMirror (底层)</h4>
                    <p className="text-sm text-muted-foreground">
                      文档模型和状态管理
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>📝 使用指南</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <h4 className="font-medium">1. 选中文字</h4>
                    <p className="text-sm text-muted-foreground">
                      选中任意文字，工具栏会自动出现
                    </p>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-medium">2. 点击 AI 按钮</h4>
                    <p className="text-sm text-muted-foreground">
                      点击工具栏中的 AI 按钮或使用快捷键
                    </p>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-medium">3. 选择操作</h4>
                    <p className="text-sm text-muted-foreground">
                      选择快速提示词或输入自定义指令
                    </p>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-medium">4. 应用结果</h4>
                    <p className="text-sm text-muted-foreground">
                      选择插入或替换生成的内容
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          
          <TabsContent value="output" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Code className="h-5 w-5" />
                  <span>Markdown 输出</span>
                </CardTitle>
                <CardDescription>
                  实时查看编辑器生成的 Markdown 内容
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[500px] w-full rounded border p-4">
                  <pre className="text-sm">
                    <code>{markdown || "开始编辑以查看 Markdown 输出..."}</code>
                  </pre>
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
