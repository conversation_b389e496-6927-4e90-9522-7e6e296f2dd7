// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

"use client";

import { useState } from "react";
import {
  EditorRoot,
  EditorContent,
  type EditorInstance,
  type JSONContent,
  StarterKit,
  Placeholder,
} from "novel";
import { useDebouncedCallback } from "use-debounce";

export interface AIEditorProps {
  initialContent?: any;
  onContentChange?: (content: JSONContent) => void;
  onMarkdownChange?: (markdown: string) => void;
  placeholder?: string;
  className?: string;
}

export function AIEditor({
  initialContent,
  onContentChange,
  onMarkdownChange,
  placeholder = "开始写作...",
  className = "",
}: AIEditorProps) {
  // 编辑器状态
  const [editor, setEditor] = useState<EditorInstance | null>(null);

  // 防抖更新函数
  const debouncedUpdate = useDebouncedCallback(
    (editor: EditorInstance) => {
      // 获取 JSON 内容
      const jsonContent = editor.getJSON();
      onContentChange?.(jsonContent);

      // 获取 Markdown 内容 (暂时注释掉)
      // if (onMarkdownChange && editor.storage.markdown) {
      //   const markdown = editor.storage.markdown.getMarkdown();
      //   onMarkdownChange(markdown);
      // }
    },
    300
  );

  // 简单的扩展配置
  const extensions = [
    StarterKit,
    Placeholder.configure({
      placeholder,
    }),
  ];

  return (
    <div className={`relative w-full min-h-[400px] border border-gray-200 dark:border-gray-800 rounded-lg overflow-hidden ${className}`}>
      <EditorRoot>
        <EditorContent
          immediatelyRender={false}
          initialContent={initialContent}
          extensions={extensions}
          className="w-full h-full"
          editorProps={{
            attributes: {
              class: "prose prose-base dark:prose-invert max-w-none p-4 focus:outline-none min-h-[400px]",
            },
          }}
          onCreate={({ editor }) => {
            setEditor(editor);
          }}
          onUpdate={({ editor }) => {
            debouncedUpdate(editor);
          }}
        />
      </EditorRoot>
    </div>
  );
}

export default AIEditor;
